@if (sensorResource.isLoading()) {
  <div class="flex justify-center items-center h-screen">
    <span class="loading loading-spinner loading-lg"></span>
  </div>
}

@if (sensorResource.error()) {
  <div class="flex justify-center items-center h-svw">
    <div class="flex flex-col alert alert-error shadow-lg w-96">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="stroke-current flex-shrink-0 h-20 w-20"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="currentColor">
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M12 8v4m0 4h.01M12 4a8 8 0 100 16 8 8 0 000-16z" />
      </svg>
      <span class="text-2xl">No se ha encontrado el sensor</span>
      <span class="text-xl underline cursor-pointer" routerLink="/sensor"
        >Volver a mis sensores</span
      >
    </div>
  </div>
} @else if (sensorResource.hasValue()) {
  <div class="flex items-baseline justify-evenly">
    <div class="flex items-baseline">
      <h2 class="text-2xl font-bold font-montserrat mx-2">
        {{ sensorResource.value()?.name }}
      </h2>
      <p class="badge badge-warning badge-soft rounded-2xl">
        {{ sensorResource.value()?.type! | sensortype }}
      </p>
    </div>

    @if (sensorsStatus() !== null) {
      <div class="flex items-center gap-1">
        @if (connectedSensors().includes(sensorId)) {
          <h3 class="font-medium mr-0.5">Online</h3>
          <div class="inline-grid *:[grid-area:1/1]">
            <div class="status status-success animate-ping"></div>
            <div class="status status-success"></div>
          </div>
        } @else {
          <h3 class="font-medium mr-0.5">Offline</h3>
          <div aria-label="error" class="status status-error"></div>
        }

        <!--  -->
      </div>
    }
  </div>
  <div class="divider"></div>

  <div class="flex mb-4">
    <div class="flex flex-col w-50 mx-3 gap-1">
      <div class="flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="2em"
          height="2em"
          viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M4.25 5.61C6.57 8.59 10 13 10 13v5c0 1.1.9 2 2 2s2-.9 2-2v-5s3.43-4.41 5.75-7.39c.51-.66.04-1.61-.8-1.61H5.04c-.83 0-1.3.95-.79 1.61" />
        </svg>
        <p class="text-lg font-semibold">Filtros</p>
      </div>
      <select class="select" (change)="onFilterChange($event)">
        @for (option of defaultFilterOptionsList; track option) {
          <option [value]="option" [selected]="filterOption() === option">
            {{ option | deffilteroption }}
          </option>
        }
      </select>
    </div>
    <div
      class="flex justify-end items-center w-full"
      [class.invisible]="!lastMeasurement()">
      <h3 class="flex flex-row">
        Última medición:&nbsp;
        <p class="font-bold">
          {{ lastMeasurement()! | munit: sensorResource.value()?.type! }}
        </p>
      </h3>
    </div>
  </div>

  @if (measurementResource.hasValue()) {
    <measurements-chart
      [sensor]="sensorResource.value()!"
      [measurements]="measurementResource.value()!.reverse()"
      [newMeasurement]="newMeasurement" />
  }
}
