import { Injectable, signal, inject } from '@angular/core'
import { MeasurementSocketService } from './measurements-socket.service'
import { EmqxApiService } from '@common/services/emqx-api.service'
import { AuthService } from '@auth/services/auth.service'

export type SensorStatus = {
  connected: boolean
  sensorId: string
}

@Injectable({ providedIn: 'root' })
export class SensorsStatusService {
  sensorsStatus = signal<SensorStatus[] | null>([])

  measurementService = inject(MeasurementSocketService)
  emqxApiService = inject(EmqxApiService)
  authService = inject(AuthService)
  intervalId: any | null = null
  constructor() {
    this.fetchSensorsStatus()
    this.intervalId = setInterval(() => {
      if (this.authService.token() === null) {
        this.clearInterval()
        return
      }
      this.fetchSensorsStatus()
    }, 1000);
  }

  fetchSensorsStatus() {
    this.emqxApiService.getActiveClients().subscribe({
      next: (response) => {
        if (response) {
          this.sensorsStatus.set([])
          response.data.forEach(client => {
            if (client.clientid.startsWith('sensor_') && this.authService.user()!.sensors.find(a => a.id === client.clientid.substring(7))) {
              this.sensorsStatus.update(value => {
                if (value === null || value.length === 0) return [{ sensorId: client.clientid.substring(7), connected: true }]

                return value.map(s => {
                  if (s.sensorId === client.clientid) {
                    return {
                      sensorId: s.sensorId,
                      connected: true,
                    }
                  }
                  return s
                }).concat([{ sensorId: client.clientid.substring(7), connected: true }]).filter(s => s !== undefined)
              })
            }
          })
        }
      },
      error: (error) => {
        console.error('Error fetching EMQX clients:', error)
      }
    })
  }

  clearInterval(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }
  }
}
