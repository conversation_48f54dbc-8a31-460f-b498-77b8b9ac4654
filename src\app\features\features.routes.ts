import { Routes } from '@angular/router'
import { FeaturesLayoutComponent } from './layout/features-layout.component'
import { DashboardPageComponent } from './dashboard/pages/dashboard-page/dashboard-page.component'

export const featuresRoutes: Routes = [
  {
    path: '',
    component: FeaturesLayoutComponent,
    children: [
      {
        path: '',
        component: DashboardPageComponent,
      },
      {
        path: 'sensor',
        loadChildren: () => import('./sensor/sensor.routes'),
      },
      {
        path: 'actuator',
        loadChildren: () => import('./actuators/actuator.routes'),
      },
      {
        path: 'trigger',
        loadChildren: () => import('./trigger/trigger.routes'),
      },
    ],
  },
]

export default featuresRoutes
