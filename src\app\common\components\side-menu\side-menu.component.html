<div class="drawer lg:drawer-open">
  <input id="my-drawer-2" type="checkbox" class="drawer-toggle" />
  <div class="drawer-content">
    <!-- Page content here -->
    <ng-content></ng-content>
  </div>
  <div class="drawer-side">
    <label
      for="my-drawer-2"
      aria-label="close sidebar"
      class="drawer-overlay"></label>
    <ul class="menu bg-base-200 text-base-content min-h-full w-80 p-4 gap-2">
      <p class="text-2xl font-bold mb-2">Menú</p>
      <!-- Sidebar content here -->
      <!-- TODO: Componetizar botones para hacerlo con @for -->
       @for (element of sideMenuElements; track element.name) {
        <li>
          <label
            for="my-drawer-2"
            routerLink="{{ element.route }}"
            class="btn btn-secondary w-full drawer-button">
            <div class="w-6 h-6" [innerHTML]="element.safeIcon"></div>
            <a>{{ element.name }}</a>
          </label>
        </li>
      }

    </ul>
  </div>
</div>
