<label class="label pb-3 text-xl pt-2"><PERSON>ra</label>
<div class="pb-5">
  <input
    type="time"
    id="appt-time"
    name="appt-time"
    class="input"
    [(ngModel)]="hour"
    (change)="onHourChange()"
    [ngModelOptions]="{ standalone: true }"/>
</div>
@if (this.error() != '') {
  <span class="label-text-alt text-red-400">
    {{ this.error() }}
  </span>
  <br>
}
<label class="label pb-3 text-xl">Días</label>
<div class="flex w-full justify-between pr-2 pl-2 mb-5">
  @for (day of days; track $index) {
    <div>
      <input
        id="{{ day.name }}-option"
        type="checkbox"
        [value]="day.value"
        (click)="onDayChange(day.value)"
        [(ngModel)]="selectedDays[day.name]"
        [ngModelOptions]="{ standalone: true }"
        class="hidden peer" />
      <label
        for="{{ day.name }}-option"
        class="inline-flex items-center justify-between w-10 h-10 text-gray-500 bg-white border-2 border-gray-200 rounded-full cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 peer-checked:border-blue-600 dark:peer-checked:border-blue-600 hover:text-gray-600 dark:peer-checked:text-gray-300 peer-checked:text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
        <div class="w-full text-sm flex align-middle justify-center">
          {{ day.name }}
        </div>
      </label>
    </div>
  }
</div>
@if (daysError() != '') {
  <span class="label-text-alt text-red-400">
    {{ daysError() }}
  </span>
}
