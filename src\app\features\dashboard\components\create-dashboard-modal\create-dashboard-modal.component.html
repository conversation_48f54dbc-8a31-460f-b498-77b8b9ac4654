<dialog id="create_dashboard_modal" class="modal">
  <div class="flex flex-col modal-box items-center justify-center">
    <h2 class="text-3xl font-bold mb-5">Nuevo Dashboard</h2>
    <form
      autocomplete="off"
      [formGroup]="createForm"
      (ngSubmit)="onSubmitCreate()">
      <div class="flex mx-4 items-center justify-center">
        <fieldset class="fieldset">
          <label class="label">Nombre</label>
          <select class="select" formControlName="sensorId" >
            <option value="">--Seleccione un sensor--</option>
            @for (sensor of sensors(); track $index) {
              <option [value]="sensor.id">{{ sensor.name }}</option>
            }
          </select>
          @if (createFormUtils.isInvalidField('sensorId')) {
            <span class="label-text-alt text-red-400">
              {{ createFormUtils.getFieldError('sensorId') }}
            </span>
          }
          <label class="label">Tipo de indicador</label>
          <select class="select" formControlName="type">
            <option value="">--Seleccione un tipo--</option>
            @for (type of dashboardTypes(); track type) {
              <option [value]="type">{{ type | dashboardtype }}</option>
            }
          </select>
          @if (createFormUtils.isInvalidField('type')) {
            <span class="label-text-alt text-red-400">
              {{ createFormUtils.getFieldError('type') }}
            </span>
          }

          <div class="modal-action">
            <button class="btn btn-primary w-30" type="submit">
              @if (!isCreating()) {
                Crear
              } @else {
                <span class="loading loading-dots loading-lg"></span>
              }
            </button>
            <form method="dialog">
              <!-- if there is a button in form, it will close the modal -->
              <button
                class="btn btn-outline btn-error w-30"
                id="cancel-button"
                (click)="onCancel()"
                #cancelButton>
                Cancelar
              </button>
            </form>
          </div>
        </fieldset>
      </div>
    </form>
  </div>
</dialog>
