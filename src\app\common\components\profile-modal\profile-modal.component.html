<input type="checkbox" id="profile_modal" class="modal-toggle" />
<div class="modal" role="dialog">
  <div class="modal-box">
    <h3 class="text-lg font-bold text-center mb-5">Perfil de usuario</h3>
    <div class="flex text-center items-center">
      <p class="py-4 mx-2">Nombre:</p>
      <input
        type="text"
        class="input w-fit"
        [value]="name()"
        [disabled]="true" />
    </div>
    <div class="flex text-center items-center">
      <p class="py-4 mx-2">Correo electrónico:</p>
      <input
        type="text"
        class="input w-fit"
        [value]="email()"
        [disabled]="true" />
    </div>
    <br />
    <div class="flex justify-end items-end">
      <button class="btn btn-error btn-outline" (click)="onLogout()">
        @if (!isLoggingOut()) {
          <PERSON><PERSON><PERSON>
        } @else {
          <span class="loading loading-dots loading-lg"></span>
        }
      </button>
    </div>
  </div>

  <label class="modal-backdrop" for="profile_modal">Close</label>
</div>
