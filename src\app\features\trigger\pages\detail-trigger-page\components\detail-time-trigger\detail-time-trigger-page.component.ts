import {
  Component,
  computed,
  EventEmitter,
  inject,
  input,
  output,
  Output,
  signal,
  WritableSignal,
} from '@angular/core'
import {
  FormBuilder,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { Router } from '@angular/router'
import { AuthService } from '@auth/services/auth.service'
import { Trigger } from 'src/app/features/trigger/interfaces/trigger'

@Component({
  selector: 'app-detail-time-trigger-page',
  imports: [ReactiveFormsModule, FormsModule],
  templateUrl: './detail-time-trigger-page.component.html',
})
export class DetailTimeTriggerPageComponent {
  days = [
    { name: 'L', value: 1 },
    { name: 'M', value: 2 },
    { name: 'X', value: 3 },
    { name: 'J', value: 4 },
    { name: 'V', value: 5 },
    { name: 'S', value: 6 },
    { name: 'D', value: 7 },
  ]
  selectedDays: { [key: string]: boolean } = {}

  private authService = inject(AuthService)
  trigger = computed(() => {
    return this.authService.user()?.triggers.find(
      t => t.id == this.router.url.split('/')[3]
    )
  })

  private router = inject(Router)

  triggerDays = computed(() => {
    console.log(this.trigger())
    return this.trigger()?.objectiveHour?.split(' ')[4].split(',').map(Number)
  })
  constructor() {
    this.days.forEach(day => {
      if (this.triggerDays()?.includes(day.value)) {
        this.selectedDays[day.name] = true
      } else {
        this.selectedDays[day.name] = false
      }
    })
  }

  getSelectedDays(): number[] {
    return this.days
      .filter(day => this.selectedDays[day.name] === true)
      .map(day => day.value)
  }
}
