import { Routes } from '@angular/router'
import { AuthenticatedGuard } from '@auth/guards/authenticated.guard'
import { NotAuthenticatedGuard } from '@auth/guards/not-authenticated.guard'

export const routes: Routes = [
  {
    path: 'auth',
    loadChildren: () => import('./auth/auth.routes'),
    canMatch: [NotAuthenticatedGuard],
  },
  {
    path: '',
    loadChildren: () => import('./features/features.routes'),
    canMatch: [AuthenticatedGuard],
  },
  {
    path: '**',
    redirectTo: 'auth/login',
  },
]
