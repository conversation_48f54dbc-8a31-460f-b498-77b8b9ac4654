import { Injectable, Optional, inject } from '@angular/core'
import { Observable, catchError, map, of, throwError } from 'rxjs'
import { HttpClient, HttpHeaders } from '@angular/common/http'
import { AuthService } from '@auth/services/auth.service'
import { environment } from '@environments/environment'
import {
  Messaging,
  onMessage,
  getToken,
  getMessaging,
} from 'firebase/messaging'
import * as firebase from 'firebase/app'

@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  public messaggingFirebase: Messaging
  private http = inject(HttpClient)
  private authService = inject(AuthService)

  getNotificationTokens(): Observable<string[] | null> {
    return this.http
      .get<any>(`${environment.baseUrl}/auth/get-notification-tokens`, {
        headers: {
          Authorization: `Bearer ${this.authService.token()}`,
        },
      })
      .pipe(
        catchError(error => {
          console.error('Error fetching EMQX clients:', error)
          return of(null)
        })
      )
  }

  async saveNotificationToken() {
    const url = `${environment.baseUrl}/auth/save-notification-token`
    const token = await this.buildToken()
    this.getNotificationTokens().subscribe(tokens => {
      if (tokens && tokens.includes(token)) return
      const body = { token }
      const headers = new HttpHeaders().set(
        'Authorization',
        `Bearer ${this.authService.token()}`
      )
      this.http.post<any>(url, body, { headers }).subscribe()
    })
  }

  public async buildToken() {
    const tokenFirebase = await getToken(this.messaggingFirebase, {
      vapidKey: environment.firebase.vpaidKey,
    })
    return tokenFirebase
  }

  constructor() {
    const app = firebase.initializeApp(environment.firebase)
    this.messaggingFirebase = getMessaging(app)
  }
}
