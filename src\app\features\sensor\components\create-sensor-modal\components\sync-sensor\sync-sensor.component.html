<div class="max-w-4xl mx-auto p-8 bg-white rounded-lg shadow-lg">
  <h2 class="text-4xl font-bold mb-8 text-center text-gray-800">Pasos para sincronizar sensor</h2>

  <ol class="space-y-6 text-lg">
    <li class="flex items-start">
      <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">1</span>
      <span class="pt-1">Encienda el sensor</span>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">2</span>
      <span class="pt-1">Conectarse en su dispositivo a la red <span class="font-semibold text-blue-600">"SensorWIFI"</span></span>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">3</span>
      <span class="pt-1">Ingrese a la siguiente dirección en su navegador: <span class="font-mono bg-gray-100 px-2 py-1 rounded text-blue-600">10.0.0.2</span></span>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">4</span>
      <span class="pt-1">Ingrese los datos de su red WIFI con acceso a internet</span>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">5</span>
      <div class="pt-1 flex-1">
        <p class="mb-4">Agregue los siguientes datos <span class="text-red-600 font-semibold">(Si cierra esta pestaña, los datos no se volverán a mostrar)</span>:</p>

        <div class="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300 space-y-4">
          <!-- Token Section -->
          <div class="bg-white p-4 rounded-lg shadow-sm border">
            <label class="block text-sm font-semibold text-gray-700 mb-2">Token:</label>
            <div class="flex items-center gap-3">
              <input
                type="text"
                [value]="sensorToken()"
                readonly
                class="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md font-mono text-sm text-gray-800 focus:outline-none"
              >
              <button
                (click)="copyToClipboard(sensorToken(), 'Token')"
                class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200 flex items-center gap-2 font-medium"
                title="Copiar token"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                Copiar
              </button>
            </div>
          </div>

          <!-- ID Section -->
          <div class="bg-white p-4 rounded-lg shadow-sm border">
            <label class="block text-sm font-semibold text-gray-700 mb-2">ID:</label>
            <div class="flex items-center gap-3">
              <input
                type="text"
                [value]="sensorId()"
                readonly
                class="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md font-mono text-sm text-gray-800 focus:outline-none"
              >
              <button
                (click)="copyToClipboard(sensorId(), 'ID')"
                class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200 flex items-center gap-2 font-medium"
                title="Copiar ID"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                Copiar
              </button>
            </div>
          </div>
        </div>
      </div>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">6</span>
      <span class="pt-1">Presione el botón <span class="font-semibold text-green-600">"Sincronizar"</span></span>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">7</span>
      <span class="pt-1">Vuelva a conectar su dispositivo a la red WiFi con acceso a internet</span>
    </li>
  </ol>

  <!-- Close Button Section -->
  <div class="flex justify-center mt-12 pt-8 border-t border-gray-200">
    <button
      class="px-8 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-semibold text-lg transition-colors duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
      (click)="closeModal()"
    >
      Cerrar
    </button>
  </div>

  <!-- Copy Success Message -->
  @if (copyMessage()) {
    <div class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300">
      {{ copyMessage() }}
    </div>
  }
</div>

