export class DateUtils {
  /**
   * Resta un número de horas a una fecha dada.
   * @param date Fecha base.
   * @param hours Número de horas a restar.
   * @returns Nueva fecha con las horas restadas.
   */
  static subtractHours(date: Date, hours: number): Date {
    return new Date(date.getTime() - hours * 60 * 60 * 1000)
  }

  /**
   * Resta un número de días a una fecha dada.
   * @param date Fecha base.
   * @param days Número de días a restar.
   * @returns Nueva fecha con los días restados.
   */
  static subtractDays(date: Date, days: number): Date {
    return new Date(date.getTime() - days * 24 * 60 * 60 * 1000)
  }

  /**
   * Resta un número de semanas a una fecha dada.
   * @param date Fecha base.
   * @param weeks Número de semanas a restar.
   * @returns Nueva fecha con las semanas restadas.
   */
  static subtractWeeks(date: Date, weeks: number): Date {
    return new Date(date.getTime() - weeks * 7 * 24 * 60 * 60 * 1000)
  }
}
