import { Pipe, PipeTransform } from '@angular/core'
import { SensorType } from '@auth/interfaces/user'

export const sensorTypeTranslator: Map<string, string> = new Map([
  [SensorType.LEVEL, 'Nivel de Tanque'],
  [SensorType.PRESSURE, 'Presión'],
  [SensorType.PH, 'Acidez (pH)'],
  [SensorType.TDS, 'Sólidos disueltos totales (TDS)'],
])

@Pipe({
  name: 'sensortype',
})
export class SensorTypePipe implements PipeTransform {
  transform(value: string): string {
    return sensorTypeTranslator.get(value) ?? 'Tipo desconocido'
  }
}
