import {
  Component,
  computed,
  EventEmitter,
  inject,
  input,
  output,
  Output,
  signal,
  WritableSignal,
} from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { Router } from '@angular/router'
import { Sensor } from '@auth/interfaces/user'
import { AuthService } from '@auth/services/auth.service'
import { MeasurementUnitPipe } from 'src/app/features/sensor/pipes/measurement-unit.pipe'
import { ComparisonTypesEnum } from 'src/app/features/trigger/interfaces/comparison-types'
import { Trigger } from 'src/app/features/trigger/interfaces/trigger'
import { ComparisonPipe } from 'src/app/features/trigger/pipes/comparisons.pipe'

@Component({
  selector: 'app-detail-sensor-trigger-page',
  imports: [
    ReactiveFormsModule,
    FormsModule,
    ComparisonPipe,
    MeasurementUnitPipe,
  ],
  templateUrl: './detail-sensor-trigger-page.component.html',
})
export class DetailSensorTriggerPageComponent {
  private authService = inject(AuthService)

  trigger = computed(() => {
    return this.authService.user()?.triggers.find(
      t => t.id == this.router.url.split('/')[3]
    )
  })

  private router = inject(Router)
  userSensors: WritableSignal<Sensor[]> = signal([])

  selectedSensor = computed(() => {
    return this.trigger()?.sensorId
  })

  selectedSensorType = computed(() => {
    const sensor = this.userSensors().find(s => s.id == this.selectedSensor())
    return sensor ? sensor.type : null
  })

  comparisons = computed(() => {
    return Object.values(ComparisonTypesEnum)
  })

  selectedComparison = computed(() => {
    return this.trigger()?.comparison
  })

  initialMeasure = computed(() => {
    return this.trigger()?.objectiveMeasures
      ? this.trigger()?.objectiveMeasures![0]
      : 0
  })

  endMeasure = computed(() => {
    return this.trigger()?.objectiveMeasures &&
      (this.selectedComparison() == this.comparisons()[6] ||
        this.selectedComparison() == this.comparisons()[7])
      ? this.trigger()?.objectiveMeasures![1]
      : 0
  })

  constructor() {
    this.userSensors.set(
      this.authService.user()?.sensors ? this.authService.user()!.sensors : []
    )
  }
}
