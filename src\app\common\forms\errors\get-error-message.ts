import { ValidationErrors } from '@angular/forms'
import { emailPattern } from '../patterns/email.pattern'
import { fullNamePattern } from '../patterns/full-name.pattern'

export const getErrorMessage = (errors: ValidationErrors): string | null => {
  for (const key of Object.keys(errors)) {
    switch (key) {
      case 'required':
        return 'Este campo es requerido'
      case 'minlength':
        return `Minimo ${errors['minlength'].requiredLength} caracteres`
      case 'min':
        return `El valor mínimo es ${errors['min'].min}`
      case 'email':
        return 'El formato del email es incorrecto'
      case 'pattern':
        if (errors['pattern'].requiredPattern === emailPattern) {
          return 'El correo electrónico no es válido'
        }
        if (
          (errors['pattern'].requiredPattern as string).includes(
            fullNamePattern
          )
        ) {
          return 'El formato debe ser de nombre y apellido'
        }
        return 'El formato del campo es incorrecto'
      case 'fieldsNotEqual':
        return 'Las contraseñas deben ser iguales'
      case 'requiredNotFilled':
        return 'Introduzca la clave de encriptación'
      default:
        return 'Error de validación no controlado'
    }
  }

  return null
}
