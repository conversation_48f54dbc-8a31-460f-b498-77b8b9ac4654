import { Component, input, output, signal, OnInit } from '@angular/core'
import { FormsModule } from '@angular/forms'

@Component({
  selector: 'app-update-actuator-modal',
  imports: [FormsModule],
  templateUrl: './update-actuator-modal.component.html',
})
export class UpdateActuatorModalComponent implements OnInit {
  // Inputs
  actuatorId = input.required<string>()
  currentName = input.required<string>()
  
  // Outputs
  updateActuator = output<{ actuatorId: string; name: string }>()
  cancelUpdate = output<void>()
  
  // Local state
  newName = signal('')
  
  ngOnInit() {
    // Initialize with current name when modal opens
    this.newName.set(this.currentName())
  }
  
  onSubmit() {
    const trimmedName = this.newName().trim()
    if (trimmedName && trimmedName !== this.currentName()) {
      this.updateActuator.emit({
        actuatorId: this.actuatorId(),
        name: trimmedName
      })
    }
    this.closeModal()
  }
  
  closeModal() {
    const modal = document.getElementById('update_actuator_modal') as HTMLDialogElement
    modal?.close()
  }
  
  onCancel() {
    // Reset to current name
    this.newName.set(this.currentName())
    this.closeModal()
    this.cancelUpdate.emit()
  }
}
