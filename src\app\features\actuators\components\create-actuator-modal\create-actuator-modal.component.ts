import { Component, signal } from '@angular/core'
import { ReactiveFormsModule } from '@angular/forms'
import { CreateActuatorComponent } from './components/create-actuator/create-actuator.component'
import { SyncActuatorComponent } from './components/sync-actuator/sync-actuator.component'
import { SyncSuccessMessageComponent } from './components/sync-success-message/sync-success-message.component'

@Component({
  selector: 'app-create-actuator-modal',
  imports: [
    ReactiveFormsModule,
    CreateActuatorComponent,
    SyncActuatorComponent,
    SyncSuccessMessageComponent,
  ],
  templateUrl: './create-actuator-modal.component.html',
})
export class CreateActuatorModalComponent {
  actuatorToken = signal('')
  isSyncing = signal(false)
  syncSuccessful = signal(false)
}
