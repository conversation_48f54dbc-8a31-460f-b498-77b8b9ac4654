import { Component, signal } from '@angular/core'
import { ReactiveFormsModule } from '@angular/forms'
import { CreateSensorComponent } from './components/create-sensor/create-sensor.component'
import { SyncSensorComponent } from './components/sync-sensor/sync-sensor.component'
import { SyncSuccessMessageComponent } from './components/sync-success-message/sync-success-message.component'

@Component({
  selector: 'app-create-sensor-modal',
  imports: [
    ReactiveFormsModule,
    CreateSensorComponent,
    SyncSensorComponent,
  ],
  templateUrl: './create-sensor-modal.component.html',
})
export class CreateSensorModalComponent {
  sensorToken = signal('')
  sensorId = signal('')
  isSyncing = signal(false)
  syncSuccessful = signal(false)

}
