<h2 class="text-3xl font-bold mb-5">Nuevo Sensor</h2>
<form autocomplete="off" [formGroup]="createForm" (ngSubmit)="onSubmitCreate()">
  <div class="flex mx-4 items-center justify-center">
    <fieldset class="fieldset">
      <label class="label">Nombre</label>
      <input type="text" class="input" formControlName="name" />
      @if (createFormUtils.isInvalidField('name')) {
        <span class="label-text-alt text-red-400">
          {{ createFormUtils.getFieldError('name') }}
        </span>
      }
      <label class="label">Tipo de sensor</label>
      <select class="select" formControlName="type">
        <option value="">--Seleccione un tipo--</option>
        @for (type of sensorTypes(); track type) {
          <option [value]="type">{{ type | sensortype }}</option>
        }
      </select>
      @if (createFormUtils.isInvalidField('type')) {
        <span class="label-text-alt text-red-400">
          {{ createFormUtils.getFieldError('type') }}
        </span>
      }

      <div class="modal-action">
        <button class="btn btn-primary w-30" type="submit">
          @if (!isCreating()) {
            Crear
          } @else {
            <span class="loading loading-dots loading-lg"></span>
          }
        </button>
        <form method="dialog">
          <!-- if there is a button in form, it will close the modal -->
          <button
            class="btn btn-outline btn-error w-30"
            (click)="onCancel()"
            #cancelButton>
            Cancelar
          </button>
        </form>
      </div>
    </fieldset>
  </div>
</form>
