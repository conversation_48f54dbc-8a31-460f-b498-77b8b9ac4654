import {
  Component,
  input,
  OnInit,
  viewChild,
  computed,
  effect,
  Signal,
  inject,
  signal,
  output,
  OnDestroy,
} from '@angular/core'
import { Sensor } from '@auth/interfaces/user'
import { ChartConfiguration } from 'chart.js'
import { BaseChartDirective } from 'ng2-charts'
import {
  SensorTypePipe,
  sensorTypeTranslator,
} from 'src/app/features/sensor/pipes/sensor-type.pipe'
import { FindManyMeasurementsResponse } from 'src/app/features/sensor/services/api/api-responses/find-many-measurements.response'
import { MeasurementSocketService } from 'src/app/features/sensor/services/ws/measurements-socket.service'
import { NewMeasurement } from '../../pages/dashboard-page/dashboard-page.component'
import { DashboardTypesEnum } from '../../interfaces/dashboard-types.enum'
import { ConfirmPopupComponent } from '@common/components/confirm-popup/confirm-popup.component'
import { DashboardApiService } from '../../services/api/dashboard-api.service'

@Component({
  selector: 'dashboard-chart',
  imports: [BaseChartDirective, SensorTypePipe, ConfirmPopupComponent],
  templateUrl: './dashboard-chart.component.html',
})
export class DashboardChartComponent implements OnInit, OnDestroy{

  showConfirmModal = signal<boolean>(false)
  deleted = output<boolean>()
  dashboardApiService = inject(DashboardApiService)
  measurementService = inject(MeasurementSocketService)

  id = input.required<string>()
  sensor = input.required<Sensor>()

  measurements = input.required<FindManyMeasurementsResponse[]>()

  chart = viewChild.required(BaseChartDirective)

  newMeasurement = signal<NewMeasurement | null>(null)
  private onNewMeasurementEffect = effect(() => {
    if (this.newMeasurement() !== null) {
      this.pushOne(this.newMeasurement()?.measurement!)
    }
  })

  //TODO: Mejorar el aspecto de la tabla cuando hay muchos registros
  //Opciones:
  //1. Agrupar registros
  //2. Eliminar los puntos de las medidas

  measurementsData: ChartConfiguration['data'] = {
    datasets: [
      {
        data: [],
        yAxisID: 'y1',
        backgroundColor: 'rgba(0,116,255,0.3)',
        borderColor: 'rgba(0,116,255,1)',
        pointBackgroundColor: 'rgba(148,159,177,1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(148,159,177,0.8)',
        fill: 'origin',
      },
    ],
    labels: [],
  }

  measurementsOptions: ChartConfiguration['options'] = {
    responsive: true,
  }

  initLabel(): void {
    const newLabel = sensorTypeTranslator.get(this.sensor().type)
    this.measurementsData.datasets[0].label = newLabel
    this.chart().update()
  }

  initData(): void {
    const data = this.measurements().map(measurement => measurement.measurement)
    this.measurementsData.datasets[0].data = data
    this.chart().update()
  }

  initTime(): void {
    const timeLabels = this.measurements().map(measurement => {
      const date = new Date(measurement.timestamp)
      return this.dateParser(date)
    })
    this.measurementsData.labels = timeLabels
    this.chart().update()
  }

  pushOne(measurement: number): void {
    this.measurementsData.datasets[0].data.push(measurement)
    this.measurementsData.labels?.push(this.dateParser(new Date(Date.now())))

    this.chart().update()
  }

  private dateParser(date: Date): string {
    return `${date.getDay()}/${date.getMonth()}-${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`
  }

  ngOnInit(): void {
    this.initLabel()
    this.initData()
    this.initTime()
    this.measurementService.sendMessage('subscribe', {
      sensorId: this.sensor().id,
    })
    this.measurementService.onMessage('measurement', body => {
      if (body.sensorId === this.sensor().id) {
        this.newMeasurement.set({ measurement: body.measurement })
      }
    })
  }

  ngOnDestroy(): void {
    this.measurementService.sendMessage('unsubscribe', {
      sensorId: this.sensor().id,
    })
  }

  confirmedDelete (event: boolean){
    this.showConfirmModal.set(false)
    if (event === false) return
    this.dashboardApiService.delete(this.id()).subscribe((response)=> {
      this.showConfirmModal.set(false)
      this.deleted.emit(true)
    })
  }

}
