<div class="navbar bg-base-100 shadow-sm max-w-full">
  <div for="my-drawer-2" class="flex-none">
    <label for="my-drawer-2" class="btn btn-square drawer-button lg:hidden">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        class="inline-block h-5 w-5 stroke-current">
        <path
          stroke-linecap="round"
          strokeº-linejoin="round"
          stroke-width="2"
          d="M4 6h16M4 12h16M4 18h16"></path>
      </svg>
    </label>
  </div>
  <div>
    <div class="mx-3">
      <app-mini-title />
    </div>
  </div>

  <div class="flex w-full justify-end items-end">
    <label for="profile_modal" class="avatar avatar-placeholder sm:hidden">
      <div class="bg-neutral text-neutral-content w-12 rounded-full">
        <span>{{ user?.name | initials }}</span>
      </div>
    </label>
    <label for="profile_modal" class="btn hidden sm:flex">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4s-4 1.79-4 4s1.79 4 4 4m0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4" />
      </svg>
      {{ user?.name }}
    </label>
  </div>
</div>
