<div class="flex flex-col items-center justify-center">
  <h2 class="text-3xl font-bold mb-5">Sincronización exitosa</h2>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="4em"
    height="4em"
    viewBox="0 0 24 24">
    <g
      fill="none"
      stroke="#0fa841"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2">
      <path
        fill="#0fa841"
        fill-opacity="0.3"
        d="M3 12c0 -4.97 4.03 -9 9 -9c4.97 0 9 4.03 9 9c0 4.97 -4.03 9 -9 9c-4.97 0 -9 -4.03 -9 -9Z">
        <animate
          fill="freeze"
          attributeName="fill-opacity"
          dur="0.15s"
          values="0.3;0" />
      </path>
      <path stroke-dasharray="14" stroke-dashoffset="14" d="M8 12l3 3l5 -5">
        <animate
          fill="freeze"
          attributeName="stroke-dashoffset"
          begin="0.15s"
          dur="0.2s"
          values="14;0" />
      </path>
    </g>
  </svg>
  <form method="dialog">
    <!-- if there is a button in form, it will close the modal -->
    <button
      class="btn btn-outline btn-error w-30"
      [class.hidden]="!isClosingAfterSuccess()"
      #cancelButton>
      Cancelar
    </button>
  </form>
</div>
