import { Pipe, PipeTransform } from '@angular/core'
import { DashboardTypesEnum } from '../interfaces/dashboard-types.enum'

export const dashboardTypeTranslator: Map<string, string> = new Map([
  [DashboardTypesEnum.AVERAGE, 'Promedio'],
  [DashboardTypesEnum.MAXIMUM, 'Máxi<PERSON>'],
  [DashboardTypesEnum.MEASURE, 'Mediciones'],
  [DashboardTypesEnum.MINIMUM, 'Mínimos'],
])

@Pipe({
  name: 'dashboardtype',
})
export class DashboardTypePipe implements PipeTransform {
  transform(value: string): string {
    return dashboardTypeTranslator.get(value) ?? 'Tipo desconocido'
  }
}
