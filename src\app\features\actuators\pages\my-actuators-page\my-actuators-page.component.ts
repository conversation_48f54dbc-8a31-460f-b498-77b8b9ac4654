import { JsonPipe } from '@angular/common'
import {
  Component,
  computed,
  inject,
  linkedSignal,
  OnInit,
  signal,
} from '@angular/core'
import { AuthService } from '@auth/services/auth.service'
import { ActuatorTypePipe } from '../../pipes/actuator-type.pipe'
import { RouterLink } from '@angular/router'
import { CreateActuatorModalComponent } from '../../components/create-actuator-modal/create-actuator-modal.component'
import { UpdateActuatorModalComponent } from '../../components/update-actuator-modal/update-actuator-modal.component'
import { ActuatorsOpenStatusService } from '../../services/ws/actuators-open-status.service'
import { ActuatorSocketService } from '../../services/ws/actuator-socket.service'
import { ActuatorApiService } from '../../services/actuator-api.service'
import { ConfirmPopupComponent } from '@common/components/confirm-popup/confirm-popup.component'

@Component({
  selector: 'app-my-actuators-page',
  imports: [ActuatorTypePipe, CreateActuatorModalComponent, UpdateActuatorModalComponent, ConfirmPopupComponent],
  templateUrl: './my-actuators-page.component.html',
})
export default class MyActuatorsPageComponent {
  authService = inject(AuthService)
  actuatorApiService = inject(ActuatorApiService)
  actuatorsStatusService = inject(ActuatorsOpenStatusService)
  actuatorSocketService = inject(ActuatorSocketService)

  query = signal('')

  // Track disabled actuators for debounce
  disabledActuators = signal<Set<string>>(new Set())

  // Modal state for updating actuator
  selectedActuatorForUpdate = signal<{ id: string; name: string } | null>(null)

  // Confirmation popup state
  showDeleteConfirm = signal(false)
  actuatorToDelete = signal<{ id: string; name: string } | null>(null)

  deleteConfirmMessage = computed(() => {
    const actuator = this.actuatorToDelete()
    return actuator
      ? `¿Estás seguro de que deseas eliminar el actuador "${actuator.name}"? Esto lo eliminará de todos tus triggers.`
      : ''
  })

  actuatorsStatus = this.actuatorsStatusService.actuatorsStatus

  connectedActuators = computed(() => {
    if (this.actuatorsStatus() === null) return []
    return this.actuatorsStatus()!
      .filter(s => s.connected)
      .map(s => s.actuatorId)
  })

  actuatorsOpenStatus = this.actuatorsStatusService.actuatorsOpenStatus

  openedActuators = computed(() => {
    if (this.actuatorsOpenStatus() === null) return []
    return this.actuatorsOpenStatus()!
      .filter(s => s.opened)
      .map(s => s.actuatorId)
  })

  userHasActuators = computed(() => {
    const actuators = this.authService.user()?.actuators
    return actuators !== undefined && actuators.length !== 0
  })

  actuators = computed(() => {
    const query = this.query().toLowerCase().trim()

    const actuators = this.authService.user()?.actuators!

    if (!this.query()) return actuators

    return actuators.filter(s => s.name.toLowerCase().includes(query))
  })

  isActuatorDisabled = (actuatorId: string) => {
    return this.disabledActuators().has(actuatorId)
  }

  onToggleActuator(actuatorId: string, action: "open" | "close") {
    // Check if actuator is already disabled (debounced)
    if (this.disabledActuators().has(actuatorId) || !this.connectedActuators().includes(actuatorId)) {
      return
    }

    // Add actuator to disabled set
    this.disabledActuators.update(disabled => new Set(disabled).add(actuatorId))

    // Send the message
    this.actuatorSocketService.sendMessage('toggle-actuator', {
      actuatorId,
      action,
      userId: this.authService.user()!.id,
    })

    // Re-enable after 1 second
    setTimeout(() => {
      this.disabledActuators.update(disabled => {
        const newSet = new Set(disabled)
        newSet.delete(actuatorId)
        return newSet
      })
    }, 1000)
  }

  confirmDeleteActuator(actuator: { id: string; name: string }) {
    this.actuatorToDelete.set(actuator)
    this.showDeleteConfirm.set(true)
  }

  deleteActuator(actuatorId: string) {
    this.actuatorApiService.delete(actuatorId).subscribe(response => {
      if (!response) return
      this.authService.setActuators(
        this.authService.user()!.actuators.filter(a => a.id !== actuatorId)
      )
    })
  }

  onConfirmDelete(event: boolean) {
    this.showDeleteConfirm.set(false)
    if (event === false) return
    const actuator = this.actuatorToDelete()
    if (actuator) {
      this.deleteActuator(actuator.id)
    }
    this.showDeleteConfirm.set(false)
    this.actuatorToDelete.set(null)
  }

  updateActuator(actuatorId: string, name: string) {
    this.actuatorApiService.update(actuatorId, { name }).subscribe(response => {
      if (!response) return
      this.authService.setActuators(
        this.authService.user()!.actuators.map(a => {
          if (a.id === actuatorId) {
            a.name = name
          }
          return a
        })
      )
    })
  }

  openUpdateModal(actuator: { id: string; name: string }) {
    this.selectedActuatorForUpdate.set(actuator)
  }

  onUpdateActuator(data: { actuatorId: string; name: string }) {
    this.updateActuator(data.actuatorId, data.name)
    this.selectedActuatorForUpdate.set(null)
  }
}
