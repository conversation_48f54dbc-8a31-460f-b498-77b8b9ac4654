import { Component, inject } from '@angular/core'
import { MiniTitleComponent } from '../mini-title/mini-title.component'
import { AuthService } from '@auth/services/auth.service'
import { InitialsPipe } from '@common/pipes/initials.pipe'

@Component({
  selector: 'app-navbar',
  imports: [MiniTitleComponent, InitialsPipe],
  templateUrl: './navbar.component.html',
})
export class NavbarComponent {
  user = inject(AuthService).user()
}
