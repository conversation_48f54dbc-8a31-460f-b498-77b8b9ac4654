import { Component, computed, inject, input, signal } from '@angular/core'
import { <PERSON><PERSON><PERSON>er, ReactiveFormsModule, Validators } from '@angular/forms'
import { FormGroupUtils } from '@common/forms/utils/form-group.utils'
import { CustomValidators } from '@common/forms/validators/custom-validators'
import { WiFiCredentialsService } from '@common/services/wifi-credentials.service'
import { EmqxApiService } from '@common/services/emqx-api.service'
import { ActuatorSyncService } from 'src/app/features/actuators/services/sync-service/actuator-sync.service'

@Component({
  selector: 'sync-actuator',
  imports: [ReactiveFormsModule],
  templateUrl: './sync-actuator.component.html',
})
export class SyncActuatorComponent {
  actuatorToken = input.required<string>()
  actuatorId = input.required<string>()
  setSyncSuccessful = input.required<(value: boolean) => void>()
  setIsSyncing = input.required<(value: boolean) => void>()

  private fb = inject(FormBuilder)
  private actuatorSyncService = inject(ActuatorSyncService)
  private emqxApiService = inject(EmqxApiService)
  private wifiCredentialsService = inject(WiFiCredentialsService)

  syncForm = this.fb.group(
    {
      ssid: ['', Validators.required],
      password: ['', Validators.required],
      storeCredentials: [false],
      storeEncryptedCredentials: [false],
      useCredentials: [true],
      key: ['', Validators.minLength(4)],
    },
    {
      validators: [
        CustomValidators.requiredFieldTwoIfFieldOne(
          'storeEncryptedCredentials',
          'key'
        ),
      ],
    }
  )
  syncFormUtils = new FormGroupUtils(this.syncForm)

  useCredentialsSignal = signal(true)
  storeEncryptedCredentials = signal(false)

  waitingForActuatorAnswer = signal(false)
  syncingError = signal('')
  encryptionError = signal('')

  hasCredentials = computed(() => {
    return this.wifiCredentialsService.hasCredentials()
  })

  hasEncryptedCredentials = computed(() => {
    return this.wifiCredentialsService.hasEncryptedCredentials()
  })

  willUseCredentials = computed(() => {
    const willUseCredentials =
      this.useCredentialsSignal() &&
      (this.hasCredentials() || this.hasEncryptedCredentials())
    return willUseCredentials
  })

  showEncryptionKeyField = computed(() => {
    if (this.storeEncryptedCredentials()) return true

    if (this.hasEncryptedCredentials() && this.willUseCredentials()) return true

    return false
  })

  onCancelSync() {
    this.setIsSyncing()(false)
  }

  onUseCredentialsCheckboxChange() {
    this.useCredentialsSignal.update(value => !value)
  }

  onCheckStoreCredentials() {
    const storeCredentials = this.syncForm.get('storeCredentials')?.value
    if (storeCredentials) {
      this.syncForm.patchValue({ storeEncryptedCredentials: false })
    }
  }

  onCheckStoreEncryptedCredentials() {
    this.storeEncryptedCredentials.update(value => !value)
    const storeEncryptedCredentials = this.syncForm.get(
      'storeEncryptedCredentials'
    )?.value
    if (storeEncryptedCredentials) {
      this.syncForm.patchValue({ storeCredentials: false })
    }
  }

  onChangeKeyInput() {
    this.encryptionError.set('')
  }

  onSubmitSync() {
    this.syncForm.markAllAsTouched()

    const { ssid, password, storeCredentials, storeEncryptedCredentials, key } =
      this.syncForm.value

    let syncDto = {
      ssid: ssid!,
      password: password!,
      token: this.actuatorToken(),
    }

    this.encryptionError.set('')

    if (
      this.syncForm.value.storeCredentials ||
      this.syncForm.value.storeEncryptedCredentials
    ) {
      this.syncForm.patchValue({ useCredentials: false })
    }

    let incorrectCredentials = false

    if (this.willUseCredentials()) {
      if (!this.syncForm.value.key && this.hasEncryptedCredentials()) {
        this.encryptionError.set('Introduzca la clave de encriptación')
        return
      }

      if (this.hasCredentials()) {
        const credentials = this.wifiCredentialsService.getCredentials()
        syncDto.ssid = credentials?.ssid!
        syncDto.password = credentials?.password!
      } else if (this.hasEncryptedCredentials()) {
        const credentials = this.wifiCredentialsService.decryptCredentials(key!)
        if (!credentials) {
          this.encryptionError.set('Clave de encriptación incorrecta')
          syncDto.ssid = '_'
          syncDto.password = '_'
          incorrectCredentials = true
        } else {
          syncDto.ssid = credentials.ssid
          syncDto.password = credentials.password
        }
      }

      this.syncForm.patchValue({
        ssid: syncDto.ssid,
        password: syncDto.password,
      })
    }

    const sendCredentials = this.syncForm.valid && !incorrectCredentials

    if (!sendCredentials) return

    this.syncingError.set('')
    this.waitingForActuatorAnswer.set(true)

    //TODO: Deberíamos hacer pruebas de ver el comportamiento cuando se borra
    //el localStorage en medio del proceso, por los casts que se hacen
    this.emqxApiService.getEmqxCredentials().subscribe(response => {
      this.actuatorSyncService
        .sync({
          ...syncDto,
          actuator_id: this.actuatorId(),
          mqtt_server: response?.url!,
          mqtt_port: response?.port!,
          mqtt_username: response?.username!,
          mqtt_password: response?.password!,
        })
        .subscribe(({ isSynced }) => {
          this.waitingForActuatorAnswer.set(false)
          if (!isSynced) {
            this.syncingError.set(
              'No se pudo realizar la sincronización, revise las credenciales y la conexión a internet de la red'
            )
            return
          }
          this.syncForm.reset()
          this.setSyncSuccessful()(true)

          if (storeCredentials) {
            this.wifiCredentialsService.storeCredentials({
              ssid: ssid!,
              password: password!,
            })
          } else if (storeEncryptedCredentials) {
            this.wifiCredentialsService.storeEncryptedCredentials(
              {
                ssid: ssid!,
                password: password!,
              },
              key!
            )
          }
        })
    })
  }
}
