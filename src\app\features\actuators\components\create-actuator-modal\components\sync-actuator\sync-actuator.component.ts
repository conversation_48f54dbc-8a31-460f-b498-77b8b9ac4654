import { Component, computed, inject, input, signal } from '@angular/core'

@Component({
  selector: 'sync-actuator',
  imports: [],
  templateUrl: './sync-actuator.component.html',
})
export class SyncActuatorComponent {
  actuatorToken = input.required<string>()
  actuatorId = input.required<string>()
  setSyncSuccessful = input.required<(value: boolean) => void>()
  setIsSyncing = input.required<(value: boolean) => void>()
  showTokenTooltip = signal<boolean>(false)
  showIdTooltip = signal<boolean>(false)

  closeModal() {
    const modal = document.getElementById('create_actuator_modal') as HTMLDialogElement
    modal?.close()
    this.setSyncSuccessful()(false)
    this.setIsSyncing()(false)
  }


  async copyToClipboard(text: string, type: string) {
    try {
      await navigator.clipboard.writeText(text)

      // Show appropriate tooltip
      if (type === 'Token') {
        this.showTokenTooltip.set(true)
        setTimeout(() => {
          this.showTokenTooltip.set(false)
        }, 2000)
      } else if (type === 'ID') {
        this.showIdTooltip.set(true)
        setTimeout(() => {
          this.showIdTooltip.set(false)
        }, 2000)
      }
    } catch (err) {
      console.error('Error al copiar al portapapeles:', err)
    }
  }
}
