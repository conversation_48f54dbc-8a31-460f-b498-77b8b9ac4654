import { Component, computed, inject, input, signal } from '@angular/core'

@Component({
  selector: 'sync-actuator',
  imports: [],
  templateUrl: './sync-actuator.component.html',
})
export class SyncActuatorComponent {
  actuatorToken = input.required<string>()
  actuatorId = input.required<string>()
  setSyncEnded = input.required<(value: boolean) => void>()

  closeModal() {
    this.setSyncEnded()(true)
  }
}
