import { Injectable } from '@angular/core'
import { MatSnackBar } from '@angular/material/snack-bar'

@Injectable({
  providedIn: 'root',
})
export class PwaService {
  private promptEvent: any

  constructor(private snackBar: MatSnackBar) {
    window.addEventListener('beforeinstallprompt', event => {
      event.preventDefault()
      this.promptEvent = event
      this.showInstallPrompt()
    })
  }

  private showInstallPrompt() {
    const snackBarRef = this.snackBar.open(
      'Install this app for a better experience?',
      'Install',
      { duration: 5000 }
    )

    snackBarRef.onAction().subscribe(() => {
      this.promptEvent.prompt()
      this.promptEvent.userChoice.then((choiceResult: any) => {
        if (choiceResult.outcome === 'accepted') {
          console.log('User accepted the install prompt')
        }
        this.promptEvent = null
      })
    })
  }
}
