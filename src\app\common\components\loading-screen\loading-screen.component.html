<div class="min-h-screen bg-gradient-to-br from-base-200 to-base-300 flex items-center justify-center">
  <div class="text-center animate-fade-in">
    <!-- Logo or App Title -->
    <div class="mb-8">
      <div class="mb-4">
        <!-- Tank Icon -->
        <div class="w-16 h-16 mx-auto mb-4 bg-primary rounded-full flex items-center justify-center animate-pulse">
          <svg class="w-8 h-8 text-primary-content" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
      </div>
      <h1 class="text-4xl font-bold text-primary mb-2 animate-slide-up">Tank Monitor</h1>
      <p class="text-lg text-base-content/70 animate-slide-up" style="animation-delay: 0.2s">Sistema de Monitoreo</p>
    </div>

    <!-- Loading Animation -->
    <div class="flex flex-col items-center gap-4 animate-slide-up" style="animation-delay: 0.4s">

      <!-- Loading Text -->
      <p class="text-base-content/60 animate-pulse">Verificando autenticación...</p>
    </div>

    <!-- Progress dots -->
    <div class="flex justify-center gap-2 mt-6 animate-slide-up" style="animation-delay: 0.6s">
      <div class="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
      <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
      <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
    </div>
  </div>
</div>

<style>
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fill-up {
    0% { height: 0%; }
    50% { height: 70%; }
    100% { height: 40%; }
  }

  .animate-fade-in {
    animation: fade-in 0.8s ease-out;
  }

  .animate-slide-up {
    animation: slide-up 0.6s ease-out both;
  }

  .animate-fill-up {
    animation: fill-up 2s ease-in-out infinite;
  }
</style>
