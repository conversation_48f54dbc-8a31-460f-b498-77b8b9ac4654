import { FormArray, ValidationErrors } from '@angular/forms'
import { getErrorMessage } from '../errors/get-error-message'

export class FormArrayUtils {
  isValidFieldInArray(formArray: FormArray, index: number) {
    return formArray.controls[index].errors && formArray.controls[index].touched
  }

  getFieldErrorInArray(formArray: FormArray, index: number): string | null {
    if (!formArray.controls[index]) return null

    const errors = formArray.controls[index].errors ?? {}

    return getErrorMessage(errors)
  }
}
