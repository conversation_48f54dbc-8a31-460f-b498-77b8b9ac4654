import { HttpClient } from '@angular/common/http'
import { computed, inject, Injectable, signal } from '@angular/core'
import { rxResource } from '@angular/core/rxjs-interop'
import { environment } from '@environments/environment.development'
import { catchError, map, Observable, of, switchMap } from 'rxjs'
import { RegisterDTO } from './dto/register.dto'
import { Sensor, User } from '../interfaces/user'
import { AuthResponse } from './api-responses/auth.response'
import { LoginDTO } from './dto/login.dto'
import { Actuator } from '../../features/actuators/interfaces/actuator'
import { Trigger } from '../../features/trigger/interfaces/trigger'

type IsAuthenticated = { isAuthenticated: boolean }
type AuthStatus = 'checking' | 'authenticated' | 'not-authenticated'

@Injectable({ providedIn: 'root' })
export class AuthService {
  private http = inject(HttpClient)

  private _authStatus = signal<AuthStatus>('checking')
  private _user = signal<User | null>(null)
  private _token = signal<string | null>(localStorage.getItem('token'))

  user = computed(() => this._user())
  token = computed(() => this._token())
  authStatus = computed(() => this._authStatus())

  setSensors(sensors: Sensor[]) {
    const user = this._user()!

    this._user.set({
      id: user.id,
      email: user.email,
      name: user.name,
      sensors: sensors,
      actuators: user.actuators,
      triggers: user.triggers,
    })
  }

  setActuators(actuators: Actuator[]) {
    const user = this._user()!

    this._user.set({
      id: user.id,
      email: user.email,
      name: user.name,
      sensors: user.sensors,
      actuators: actuators,
      triggers: user.triggers,
    })
  }

  setTriggers(triggers: Trigger[]) {
    const user = this._user()!

    this._user.set({
      id: user.id,
      email: user.email,
      name: user.name,
      sensors: user.sensors,
      actuators: user.actuators,
      triggers: triggers,
    })
  }

  checkStatusResource = rxResource({
    loader: () => this.checkStatus(),
  })

  checkStatus(): Observable<IsAuthenticated> {
    const token = localStorage.getItem('token')
    if (!token) {
      this.logout()
      return of({ isAuthenticated: false })
    }

    return this.http
      .get<User>(`${environment.baseUrl}/auth/current`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .pipe(
        map(resp => this.handleCurrentSuccess(resp)),
        catchError(resp => this.handleAuthError(resp))
      )
  }

  login(dto: LoginDTO): Observable<IsAuthenticated> {
    return this.http
      .post<AuthResponse>(`${environment.baseUrl}/auth/login`, dto)
      .pipe(
        map(resp => this.handleAuthSuccess(resp)),
        switchMap(() => this.checkStatus()),
        catchError(resp => this.handleAuthError(resp))
      )
  }

  register(dto: RegisterDTO): Observable<IsAuthenticated> {
    return this.http
      .post<AuthResponse>(`${environment.baseUrl}/auth/register`, {
        ...dto,
        name: dto.fullName,
      })
      .pipe(
        map(resp => this.handleAuthSuccess(resp)),
        switchMap(() => this.checkStatus()),
        catchError(resp => this.handleAuthError(resp))
      )
  }

  logout() {
    this._user.set(null)
    this._token.set(null)
    this._authStatus.set('not-authenticated')
    localStorage.removeItem('token')
  }

  private handleCurrentSuccess(resp: User) {
    this._user.set(resp)
    this._authStatus.set('authenticated')

    return { isAuthenticated: true }
  }

  private handleAuthSuccess(resp: AuthResponse) {
    this._authStatus.set('authenticated')
    this._token.set(resp.token)
    localStorage.setItem('token', resp.token)

    return { isAuthenticated: true }
  }

  private handleAuthError(err: any) {
    console.error('Error in auth service', err)
    this.logout()
    return of({ isAuthenticated: false })
  }
}
