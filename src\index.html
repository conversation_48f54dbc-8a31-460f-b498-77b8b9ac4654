<!doctype html>
<html lang="es">
  <head>
    <meta charset="utf-8" />
    <title>Tank Monitor</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover, user-scalable=no" />
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />

    <!-- Enhanced Meta tags for iOS PWA -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Tank Monitor" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="format-detection" content="telephone=no" />

    <!-- iOS-specific fixes -->
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="apple-mobile-web-app-orientations" content="portrait" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <meta name="theme-color" content="#1976d2" />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&amp;display=swap"
      rel="stylesheet" />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet" />
    <link rel="manifest" href="manifest.webmanifest" />
  </head>
  <body>
    <app-root></app-root>
    <noscript
      >Please enable JavaScript to continue using this application.</noscript
    >
  </body>
</html>
