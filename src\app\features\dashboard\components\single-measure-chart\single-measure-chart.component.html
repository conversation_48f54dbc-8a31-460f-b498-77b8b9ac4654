<div
  class="bg-gray-900 rounded-lg shadow-lg p-6 border border-gray-700 w-full h-full">
  <div class="flex w-full justify-end h-0">
    <button
      class="btn btn-circle btn-error w-6 h-6"
      (click)="showConfirmModal.set(true)">
      X
    </button>
  </div>

  <div class="mb-4">
    <h3 class="text-lg font-semibold text-gray-100">
      {{ sensor().name }} - {{ chartTypeDisplayName() }}
    </h3>
    <p class="text-sm text-gray-400">
      {{ sensor().type | sensortype }}
    </p>
  </div>

  <!-- Measurement value with unit -->
  <div class="mb-3">
    <div class="text-3xl font-bold text-gray-200">
      {{ finalMeasure().measurement| munit: sensor().type }}
    </div>
  </div>

  <!-- Date of measurement -->
  @if (chartTypeDisplayName() != 'Promedio') {
    <div class="text-sm text-gray-500">
      <span class="font-medium text-gray-400">Fecha:</span>
      {{ finalMeasure().timestamp | date: 'dd/MM/yyyy HH:mm:ss' }}
    </div>
  }
</div>

<app-confirm-popup
  [message]="`¿Seguro que quiere eliminar este indicador?`"
  (confirmed)="confirmedDelete()"
  [show]="showConfirmModal()" />
