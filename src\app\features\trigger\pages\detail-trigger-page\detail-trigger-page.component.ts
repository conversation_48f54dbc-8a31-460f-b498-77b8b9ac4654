import {
  Component,
  computed,
  inject,
  signal,
  WritableSignal,
} from '@angular/core'
import {
  FormBuilder,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { Router, RouterLink } from '@angular/router'
import { ErrorAlertComponent } from '@common/components/error-alert/error-alert.component'
import { FormGroupUtils } from '@common/forms/utils/form-group.utils'
import { Actuator } from 'src/app/features/actuators/interfaces/actuator'
import { Action, ActuatorAction } from '../../interfaces/actuator-action'
import { ActionPipe } from '../../pipes/actions.pipe'
import { TriggerApiService } from '../../services/trigger-api.service'
import { DetailTimeTriggerPageComponent } from './components/detail-time-trigger/detail-time-trigger-page.component'
import { AuthService } from '@auth/services/auth.service'
import { DetailSensorTriggerPageComponent } from './components/detail-sensor-trigger/detail-sensor-trigger-page.component'

@Component({
  selector: 'app-detail-trigger-page',
  imports: [
    RouterLink,
    ReactiveFormsModule,
    FormsModule,
    ActionPipe,
    DetailTimeTriggerPageComponent,
    DetailSensorTriggerPageComponent,
  ],
  templateUrl: './detail-trigger-page.component.html',
})
export class DetailTriggerPageComponent {
  private authService = inject(AuthService)
  trigger = computed(() => {
    return this.authService
      .user()
      ?.triggers.find(t => t.id == this.router.url.split('/')[3])
  })

  actuatorActions = computed(() => {
    return this.authService
      .user()
      ?.actuators.filter(actuator =>
        this.trigger()?.actuators.find(a => a.actuatorId == actuator.id)
      )
      .map(actuator => {
        return {
          actuatorId: actuator.id,
          actuatorName: actuator.name,
          actions: this.trigger()
            ?.actuators.find(a => a.actuatorId == actuator.id)
            ?.action.toString(),
        }
      })
  })

  private router = inject(Router)

  triggerType = computed(() => {
    return this.trigger()?.objectiveHour ? 'time' : 'sensor'
  })

  constructor() {}
}
