import { FormGroup } from '@angular/forms'
import { getErrorMessage } from '../errors/get-error-message'

export class FormGroupUtils {
  constructor(private readonly form: FormGroup) {}

  formHasError(): boolean | null {
    return this.form.errors && this.form.touched
  }

  getFormError(): string | null {
    const errors = this.form.errors ?? {}
    return getErrorMessage(errors)
  }

  isInvalidField(field: string): boolean | null {
    return this.form.controls[field].errors && this.form.controls[field].touched
  }

  getFieldError(field: string): string | null {
    if (!this.form.controls[field]) return null

    const errors = this.form.controls[field].errors ?? {}

    return getErrorMessage(errors)
  }
}
