import { JsonPipe } from '@angular/common'
import {
  Component,
  computed,
  inject,
  linkedSignal,
  OnInit,
  signal,
} from '@angular/core'
import { AuthService } from '@auth/services/auth.service'
import { SensorTypePipe } from '../../pipes/sensor-type.pipe'
import { RouterLink } from '@angular/router'
import { CreateSensorModalComponent } from '../../components/create-sensor-modal/create-sensor-modal.component'
import { UpdateSensorModalComponent } from '../../components/update-sensor-modal/update-sensor-modal.component'
import { MeasurementSocketService } from '../../services/ws/measurements-socket.service'
import { SensorsStatusService } from '../../services/ws/sensors-status.service'
import { SensorApiService } from '../../services/api/sensor-api.service'
import { ConfirmPopupComponent } from '@common/components/confirm-popup/confirm-popup.component'

@Component({
  selector: 'app-my-sensors-page',
  imports: [SensorTypePipe, CreateSensorModalComponent, UpdateSensorModalComponent, RouterLink, ConfirmPopupComponent],
  templateUrl: './my-sensors-page.component.html',
})
export default class MySensorsPageComponent {
  authService = inject(AuthService)

  sensorApiService = inject(SensorApiService)

  measurementService = inject(MeasurementSocketService)

  sensorsStatusService = inject(SensorsStatusService)

  query = signal('')

  // Modal state for updating sensor
  selectedSensorForUpdate = signal<{ id: string; name: string } | null>(null)

  // Confirmation popup state
  showDeleteConfirm = signal(false)
  sensorToDelete = signal<{ id: string; name: string } | null>(null)

  deleteConfirmMessage = computed(() => {
    const sensor = this.sensorToDelete()
    return sensor
      ? `¿Estás seguro de que deseas eliminar el sensor "${sensor.name}"? Esto eliminará los triggers y dashboards asociados.`
      : ''
  })

  sensorsStatus = this.sensorsStatusService.sensorsStatus

  connectedSensors = computed(() => {
    if (this.sensorsStatus() === null) return []
    return this.sensorsStatus()!
      .filter(s => s.connected)
      .map(s => s.sensorId)
  })

  userHasSensors = computed(() => {
    const sensors = this.authService.user()?.sensors
    return sensors !== undefined && sensors.length !== 0
  })

  sensors = computed(() => {
    const query = this.query().toLowerCase().trim()

    const sensors = this.authService.user()?.sensors!

    if (!this.query()) return sensors

    return sensors.filter(s => s.name.toLowerCase().includes(query))
  })

  confirmDeleteSensor(sensor: { id: string; name: string }) {
    this.sensorToDelete.set(sensor)
    this.showDeleteConfirm.set(true)
  }

  deleteSensor(sensorId: string) {
    this.sensorApiService.delete(sensorId).subscribe(response => {
      if (!response) return
      this.authService.setSensors(
        this.authService.user()!.sensors.filter(s => s.id !== sensorId)
      )
    })
  }

  onConfirmDelete(event: boolean) {
    this.showDeleteConfirm.set(false)
    if (event === false) return
    const sensor = this.sensorToDelete()
    if (sensor) {
      this.deleteSensor(sensor.id)
    }
    this.showDeleteConfirm.set(false)
    this.sensorToDelete.set(null)
  }

  updateSensor(sensorId: string, name: string) {
    this.sensorApiService.update(sensorId, { name }).subscribe(response => {
      if (!response) return
      this.authService.setSensors(
        this.authService.user()!.sensors.map(s => {
          if (s.id === sensorId) {
            s.name = name
          }
          return s
        })
      )
    })
  }

  openUpdateModal(sensor: { id: string; name: string }) {
    this.selectedSensorForUpdate.set(sensor)
  }

  onUpdateSensor(data: { sensorId: string; name: string }) {
    this.updateSensor(data.sensorId, data.name)
    this.selectedSensorForUpdate.set(null)
  }
}
