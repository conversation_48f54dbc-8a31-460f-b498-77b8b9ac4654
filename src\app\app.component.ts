import { Component, inject, computed, signal, OnInit } from '@angular/core'
import { RouterOutlet } from '@angular/router'
import { PwaService } from './pwa-prompt/pwa.service'
import { MatSnackBarModule } from '@angular/material/snack-bar'
import { AuthService } from '@auth/services/auth.service'
import { LoadingScreenComponent } from '@common/components/loading-screen/loading-screen.component'
import { IOSFixService } from '@common/services/ios-fix.service'

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, MatSnackBarModule, LoadingScreenComponent],
  templateUrl: './app.component.html',
})
export class AppComponent implements OnInit {
  private authService = inject(AuthService)
  private iosFixService = inject(IOSFixService)

  // Show loading for initial app load
  showLoading = signal(true)

  // Check if auth is still being verified or if we should show loading
  isCheckingAuth = computed(() =>
    this.authService.authStatus() === 'checking' || this.showLoading()
  )

  constructor(private pwaService: PwaService) {}

  ngOnInit() {
    // Initialize iOS-specific fixes
    this.iosFixService.initializeIOSFixes()

    // iOS-specific: Longer initial loading time
    const isIOS = this.iosFixService.isIOS()
    const initialDelay = isIOS ? 1500 : 1000

    // Hide loading after a minimum time to avoid flash
    setTimeout(() => {
      this.showLoading.set(false)
    }, initialDelay)

    // Also hide loading when auth check completes
    this.authService.checkStatus().subscribe({
      next: () => {
        // iOS-specific: Longer delay for smooth transition
        const transitionDelay = isIOS ? 800 : 500
        setTimeout(() => {
          this.showLoading.set(false)
        }, transitionDelay)
      },
      error: (error) => {
        console.error('Auth check error:', error)
        // Always hide loading on error
        setTimeout(() => {
          this.showLoading.set(false)
        }, 500)
      }
    })
  }
}
