import { Component, inject, computed, signal, OnInit } from '@angular/core'
import { RouterOutlet } from '@angular/router'
import { PwaService } from './pwa-prompt/pwa.service'
import { MatSnackBarModule } from '@angular/material/snack-bar'
import { AuthService } from '@auth/services/auth.service'
import { LoadingScreenComponent } from '@common/components/loading-screen/loading-screen.component'

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, MatSnackBarModule, LoadingScreenComponent],
  templateUrl: './app.component.html',
})
export class AppComponent implements OnInit {
  private authService = inject(AuthService)

  // Show loading for initial app load
  showLoading = signal(true)

  // Check if auth is still being verified or if we should show loading
  isCheckingAuth = computed(() =>
    this.authService.authStatus() === 'checking' || this.showLoading()
  )

  constructor(private pwaService: PwaService) {}

  ngOnInit() {
    // Hide loading after a minimum time to avoid flash
    setTimeout(() => {
      this.showLoading.set(false)
    }, 1000)

    // Also hide loading when auth check completes
    this.authService.checkStatus().subscribe(() => {
      // Add a small delay to ensure smooth transition
      setTimeout(() => {
        this.showLoading.set(false)
      }, 500)
    })
  }
}
