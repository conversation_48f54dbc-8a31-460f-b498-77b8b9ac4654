import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'
import { catchError, map, Observable, of } from 'rxjs'
import { CreateSensorDTO } from './dto/create-sensor.dto'
import { CreateSensorResponse } from './api-responses/create-sensor.response'
import { environment } from '@environments/environment.development'
import { AuthService } from '@auth/services/auth.service'
import { FindOneSensorDTO } from './dto/find-one-sensor.dto'
import { FindOneSensorResponse } from './api-responses/find-one-sensor.response'
import { FindManyMeasurementsResponse } from './api-responses/find-many-measurements.response'
import { FindManyMeasurementsDTO } from './dto/find-many-measurements.dto'
import { Pagination } from '@common/pagination/pagination.interface'
import { UpdateSensorDTO } from './dto/update-sensor.dto'
import { OnlyResponseDTO } from './api-responses/only-response-dto.response'
import { CreateSensorCustomCalculationDTO } from './dto/create-sensor-custom-calculation.dto'

@Injectable({ providedIn: 'root' })
export class SensorApiService {
  private http = inject(HttpClient)

  private authService = inject(AuthService)

  create(dto: CreateSensorDTO): Observable<CreateSensorResponse | null> {
    return this.http
      .post<CreateSensorResponse>(
        `${environment.baseUrl}/sensor/add-sensor-user`,
        dto,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error creando sensor: ${resp}`)
          return of(null)
        })
      )
  }

  findOne(dto: FindOneSensorDTO): Observable<FindOneSensorResponse | null> {
    return this.http
      .get<FindOneSensorResponse>(
        `${environment.baseUrl}/sensor/one/${dto.id}`,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe()
  }

  findManyMeasurements(
    dto: FindManyMeasurementsDTO
  ): Observable<FindManyMeasurementsResponse[] | null> {
    return this.http
      .get<FindManyMeasurementsResponse[]>(
        `${environment.baseUrl}/sensor/many/${dto.sensorId}`,
        {
          params: {
            startDate: dto.startDate.toISOString(),
            endDate: dto.endDate.toISOString(),
          },
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error obteniendo mediciones: ${JSON.stringify(resp)}`)
          return of(null)
        })
      )
  }

  delete(sensorId: string): Observable<OnlyResponseDTO | null> {
    return this.http
      .delete<OnlyResponseDTO>(
        `${environment.baseUrl}/sensor/delete/${sensorId}`,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error eliminando sensor: ${resp}`)
          return of(null)
        })
      )
  }

  update(id: string, dto: UpdateSensorDTO): Observable<OnlyResponseDTO | null> {
    return this.http
      .put<OnlyResponseDTO>(
        `${environment.baseUrl}/sensor/update-name/${id}`,
        dto,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error actualizando sensor: ${resp}`)
          return of(null)
        })
      )
  }

  createCustomCalculation(
    dto: CreateSensorCustomCalculationDTO
  ): Observable<OnlyResponseDTO | null> {
    return this.http
      .post<OnlyResponseDTO>(
        `${environment.baseUrl}/sensor/custom-calculation`,
        dto,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error creando cálculo custom: ${resp}`)
          return of(null)
        })
      )
  }
}
