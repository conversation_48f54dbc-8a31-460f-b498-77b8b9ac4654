import { Pipe, PipeTransform } from '@angular/core'
import { SensorType } from '@auth/interfaces/user'

const measurementUnitMap = new Map<SensorType, string>([
  [SensorType.LEVEL, 'cm'],
  [SensorType.PH, 'pH'],
  [SensorType.PRESSURE, 'psi'],
  [SensorType.TDS, 'ppm'],
])

@Pipe({
  name: 'munit',
})
export class MeasurementUnitPipe implements PipeTransform {
  transform(value: number, type: SensorType): string {
    if (type === SensorType.PH) {
      return `${measurementUnitMap.get(type)} ${value}`
    }
    return `${value}${measurementUnitMap.get(type)}`
  }
}
