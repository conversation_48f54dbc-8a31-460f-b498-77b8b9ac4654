<div class="flex gap-5">
  <div class="flex gap-5">
    <h2 class="text-3xl font-bold">Triggers</h2>
  </div>
  <div class="flex w-full justify-end items-end gap-5">
    <label class="input rounded-md hidden sm:flex">
      <svg
        class="h-[1em] opacity-50"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24">
        <g
          stroke-linejoin="round"
          stroke-linecap="round"
          stroke-width="2.5"
          fill="none"
          stroke="currentColor">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.3-4.3"></path>
        </g>
      </svg>
      <input
        type="search"
        class="grow"
        placeholder="Buscar por nombre..."
        [value]="query()"
        autofocus
        #txtSearch
        (keyup)="query.set(txtSearch.value)" />
    </label>
    <div
      class="btn btn-primary rounded-md"
      routerLink="/trigger/create">
      Crear trigger
    </div>
  </div>
</div>

<div class="flex sm:hidden items-end w-full mt-3">
  <label class="input rounded-md">
    <svg
      class="h-[1em] opacity-50"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24">
      <g
        stroke-linejoin="round"
        stroke-linecap="round"
        stroke-width="2.5"
        fill="none"
        stroke="currentColor">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.3-4.3"></path>
      </g>
    </svg>
    <input
      type="search"
      class="grow"
      placeholder="Buscar por nombre..."
      [value]="query()"
      autofocus
      #txtSearch2
      (keyup)="query.set(txtSearch2.value)" />
  </label>
</div>


@if (userHasTriggers()) {
  <div
    class="p-4 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 mt-2 gap-3 items-center justify-center">
    @for (trigger of triggers(); track trigger.id) {
      <div class="card bg-neutral rounded-md card-md shadow-sm m-3 flex w-full">
        <div class="card-body">
          <div class="flex items-center text-center">
            <h2 class="card-title w-full">{{ trigger.name }}</h2>
            <div class="flex items-center gap-1">

              <!-- <div aria-label="error" class="status status-error"></div> -->
            </div>
          </div>
          <h3 class="" >Tipo: {{trigger.objectiveHour ? "tiempo" : "sensor"}}</h3>
          <div class="justify-end card-actions gap-2">
            <button
              class="btn btn-error rounded-lg"
              (click)="confirmDeleteTrigger({ id: trigger.id, name: trigger.name })">
              Eliminar
            </button>
            <button
              class="btn btn-secondary rounded-lg"
              [routerLink]="'update/'+trigger.id">
              Editar
            </button>
            <button class="btn btn-primary rounded-lg" [routerLink]="'detail/'+trigger.id">Ver detalles</button>
          </div>
        </div>
      </div>
    }
  </div>
}
<!--  -->
@else {
  <div class="flex w-full justify-center items-center h-70">
    <div class="card w-96 bg-neutral card-md shadow-sm rounded-2xl">
      <div class="card-body flex flex-col justify-center items-center">
        <p class="text-2xl font-semibold">Aún no tienes ningún trigger</p>
        <p
          class="text-2xl font-semibold underline cursor-pointer"
          routerLink="/trigger/create">
          ¡Crea uno!
        </p>
      </div>
    </div>
  </div>
}

<app-confirm-popup
  [show]="showDeleteConfirm()"
  [message]="deleteConfirmMessage()"
  (confirmed)="onConfirmDelete($event)" />

