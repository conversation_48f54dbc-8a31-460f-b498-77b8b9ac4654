import {
  Component,
  computed,
  inject,
  input,
  Signal,
  signal,
} from '@angular/core'
import { Form<PERSON>uilder, ReactiveFormsModule, Validators } from '@angular/forms'
import { AuthService } from '@auth/services/auth.service'
import { FormGroupUtils } from '@common/forms/utils/form-group.utils'
import { ActuatorTypePipe } from '../../../../pipes/actuator-type.pipe';
import { ActuatorApiService } from '../../../../services/actuator-api.service';
import { ActuatorType } from '../../../../interfaces/actuator';

@Component({
  selector: 'create-actuator',
  imports: [ReactiveFormsModule, ActuatorTypePipe],
  templateUrl: './create-actuator.component.html',
})
export class CreateActuatorComponent {
  actuatorToken = input.required<(value: string) => void>()
  actuatorId = input.required<(value: string) => void>()
  isSyncing = input.required<(value: boolean) => void>()

  private fb = inject(FormBuilder)

  private authService = inject(AuthService)
  private actuatorApiService = inject(ActuatorApiService)

  createForm = this.fb.group({
    name: ['', Validators.required],
    type: ['', Validators.required],
  })

  createFormUtils = new FormGroupUtils(this.createForm)

  isCreating = signal(false)

  actuatorTypes = computed(() => {
    return Object.values(ActuatorType)
  })

  onCancel() {
    setTimeout(() => {
      this.createForm.reset({
        name: '',
        type: '',
      })
    }, 250)
  }

  onSubmitCreate() {
    this.createForm.markAllAsTouched()

    if (!this.createForm.valid) return

    this.isCreating.set(true)

    const { name, type } = this.createForm.value

    this.actuatorApiService
      .create({
        name: name!,
        type: type! as ActuatorType,
      })
      .subscribe(response => {
        this.isCreating.set(false)
        if (!response) return

        this.actuatorToken()(response.actuatorToken)
        this.actuatorId()(response.actuator.id)
        this.isSyncing()(true)

        this.authService.setActuators([
          ...this.authService.user()!.actuators,
          response.actuator,
        ])

        this.createForm.reset()
      })
  }
}
