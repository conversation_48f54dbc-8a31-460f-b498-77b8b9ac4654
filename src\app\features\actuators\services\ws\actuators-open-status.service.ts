import { Injectable, signal, inject, OnDestroy } from '@angular/core'
import { ActuatorSocketService } from './actuator-socket.service'
import { AuthService } from '../../../../auth/services/auth.service'
import { EmqxApiService } from '@common/services/emqx-api.service'

export type ActuatorStatus = {
  connected: boolean
  actuatorId: string
}

export type ActuatorOpenStatus = {
  opened: boolean
  actuatorId: string
}

@Injectable({ providedIn: 'root' })
export class ActuatorsOpenStatusService {
  actuatorsOpenStatus = signal<ActuatorOpenStatus[] | null>([])

  actuatorsStatus = signal<ActuatorStatus[] | null>([])

  actuatorService = inject(ActuatorSocketService)
  emqxApiService = inject(EmqxApiService)
  authService = inject(AuthService)

  intervalId: any | null = null
  constructor() {
    this.fetchActuatorsStatus()
    this.intervalId = setInterval(() => {
      if (this.authService.token() === null) {
        this.clearInterval()
        return
      }
      this.fetchActuatorsStatus()
    }, 1000);

    this.actuatorService.onMessage('actuator-open', resp => {
      this.actuatorsOpenStatus.update(value => {
        if (value === null || value.length === 0) return [resp]

        return value
          .map(s => {
            if (s.actuatorId === resp.actuatorId) {
              return
            }
            return s
          })
          .concat([resp])
          .filter(s => s !== undefined)
      })
    })
    this.authService.user()!.actuators.forEach(actuator => {
      this.actuatorService.sendMessage('get-actuator-open', {
        actuatorId: actuator.id,
      })
    })
  }

  fetchActuatorsStatus() {
    this.emqxApiService.getActiveClients().subscribe({
      next: (response) => {
        if (response) {
          this.actuatorsStatus.set([])
          response.data.forEach(client => {
            if (client.clientid.startsWith('actuator_') && this.authService.user()!.actuators.find(a => a.id === client.clientid.substring(9))) {
              this.actuatorsStatus.update(value => {
                if (value === null || value.length === 0) return [{ actuatorId: client.clientid.substring(9), connected: true }]

                return value.map(s => {
                  if (s.actuatorId === client.clientid) {
                    return {
                      actuatorId: s.actuatorId,
                      connected: true,
                    }
                  }
                  return s
                }).concat([{ actuatorId: client.clientid.substring(9), connected: true }]).filter(s => s !== undefined)
              })
            }
          })
        }
      },
      error: (error) => {
        console.error('Error fetching EMQX clients:', error)
      }
    })
  }

  clearInterval(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }
  }

}
