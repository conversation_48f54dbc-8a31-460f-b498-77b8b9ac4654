import {
  Component,
  input,
  OnInit,
  viewChild,
  computed,
  effect,
  Signal,
  inject,
} from '@angular/core'
import { Sensor } from '@auth/interfaces/user'
import { ChartConfiguration } from 'chart.js'
import { BaseChartDirective } from 'ng2-charts'
import { sensorTypeTranslator } from 'src/app/features/sensor/pipes/sensor-type.pipe'
import { FindManyMeasurementsResponse } from 'src/app/features/sensor/services/api/api-responses/find-many-measurements.response'
import { MeasurementSocketService } from 'src/app/features/sensor/services/ws/measurements-socket.service'
import { NewMeasurement } from '../../pages/sensor-page/sensor-page.component'

@Component({
  selector: 'measurements-chart',
  imports: [BaseChartDirective],
  templateUrl: './measurements-chart.component.html',
})
export class MeasurementsChartComponent implements OnInit {
  sensor = input.required<Sensor>()

  newMeasurement = input.required<Signal<NewMeasurement | null>>()

  private onNewMeasurementEffect = effect(() => {
    if (this.newMeasurement()() !== null) {
      this.pushOne(this.newMeasurement()()?.measurement!)
    }
  })

  measurements = input.required<FindManyMeasurementsResponse[]>()

  chart = viewChild.required(BaseChartDirective)

  //TODO: Mejorar el aspecto de la tabla cuando hay muchos registros
  //Opciones:
  //1. Agrupar registros
  //2. Eliminar los puntos de las medidas

  measurementsData: ChartConfiguration['data'] = {
    datasets: [
      {
        data: [],
        yAxisID: 'y1',
        backgroundColor: 'rgba(0,116,255,0.3)',
        borderColor: 'rgba(0,116,255,1)',
        pointBackgroundColor: 'rgba(148,159,177,1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(148,159,177,0.8)',
        fill: 'origin',
      },
    ],
    labels: [],
  }

  measurementsOptions: ChartConfiguration['options'] = {
    responsive: true,
  }

  initLabel(): void {
    const newLabel = sensorTypeTranslator.get(this.sensor().type)
    this.measurementsData.datasets[0].label = newLabel
    this.chart().update()
  }

  initData(): void {
    const data = this.measurements().map(measurement => measurement.measurement)
    this.measurementsData.datasets[0].data = data
    this.chart().update()
  }

  initTime(): void {
    const timeLabels = this.measurements().map(measurement => {
      const date = new Date(measurement.timestamp)
      return this.dateToHours(date)
    })
    this.measurementsData.labels = timeLabels
    this.chart().update()
  }

  pushOne(measurement: number): void {
    this.measurementsData.datasets[0].data.push(measurement)
    this.measurementsData.labels?.push(this.dateToHours(new Date(Date.now())))

    this.chart().update()
  }

  private dateToHours(date: Date): string {
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
  }

  ngOnInit(): void {
    this.initLabel()
    this.initData()
    this.initTime()
  }
}
