<label class="label pb-3 text-xl">Sensor</label>
<div class="flex w-full justify-start flex-wrap gap-5">
  <select
    class="select w-2/5"
    [(ngModel)]="selectedSensor"
    [ngModelOptions]="{ standalone: true }"
    >
    <option value="">--Seleccione un sensor--</option>
    @for (sensor of userSensors(); track sensor) {
      <option [value]="sensor.id">{{ sensor.name }}</option>
    }
  </select>
  <select
    class="select w-2/5"
    [(ngModel)]="selectedComparison"
    [ngModelOptions]="{ standalone: true }"
    >
    @for (comparison of comparisons(); track comparison) {
      <option [value]="comparison">{{ comparison | comparisonPipe }}</option>
    }
  </select>
  <div class="flex items-center w-2/5 ">
    <input type="number" class="input w-3/4 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none" [(ngModel)]="initialMeasure" [ngModelOptions]="{ standalone: true }" />
    @if (selectedSensor() != '') {
      <span class="label-text-alt text-gray-400 ml-2 text-xl">{{ (0 | munit: selectedSensorType()!).replace('0','')  }}</span>
    }
  </div>
  @if (selectedComparison() == comparisons()[6] || selectedComparison() == comparisons()[7]) {
    <div class="flex items-center w-2/5">
      <input type="number" class="input w-3/4 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none" [(ngModel)]="endMeasure" [ngModelOptions]="{ standalone: true }" />
      @if (selectedSensor() != '') {
        <span class="label-text-alt text-gray-400 ml-2 text-xl">{{ (0 | munit: selectedSensorType()!).replace('0','')  }}</span>
      }
    </div>
  }
</div>
@if (sensorError() != '') {
  <span class="label-text-alt text-red-400">
    {{ sensorError() }}
  </span>
}
