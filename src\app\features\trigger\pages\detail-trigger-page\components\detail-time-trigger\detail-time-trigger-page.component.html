
<label class="label pb-3 text-xl pt-2"><PERSON>ra</label>
<div class="pb-5">
  <input
    type="time"
    id="appt-time"
    name="appt-time"
    class="input"
    [value]="trigger()?.objectiveHour!.split(' ')[1] + ':' + trigger()?.objectiveHour!.split(' ')[0]"
    readonly/>
</div>
<label class="label pb-3 text-xl">Días</label>
<div class="flex w-full justify-between pr-2 pl-2 mb-5">
  @for (day of days; track $index) {
    <div>
      <input
        id="{{ day.name }}-option"
        type="checkbox"
        [value]="day.value"
        [checked]="selectedDays[day.name]"
        class="hidden peer"
        disabled/>
      <label
        for="{{ day.name }}-option"
        class="inline-flex items-center justify-between w-10 h-10 text-gray-500 bg-white border-2 border-gray-200 rounded-full  dark:border-gray-700 peer-checked:border-blue-600 dark:peer-checked:border-blue-600  dark:peer-checked:text-gray-300 peer-checked:text-gray-600  dark:text-gray-400 dark:bg-gray-800 ">
        <div class="w-full text-sm flex align-middle justify-center">
          {{ day.name }}
        </div>
      </label>
    </div>
  }
</div>
