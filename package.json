{"name": "tank-monitor-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/cdk": "^19.2.14", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/material": "^19.2.14", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@angular/service-worker": "^19.2.0", "@tailwindcss/postcss": "^4.1.4", "chart.js": "^4.4.9", "crypto-js": "^4.2.0", "daisyui": "^5.0.28", "firebase": "^12.0.0", "http-server": "^14.1.1", "ng2-charts": "^8.0.0", "postcss": "^8.5.3", "rxjs": "~7.8.0", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.4", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.6", "@angular/cli": "^19.2.6", "@angular/compiler-cli": "^19.2.0", "@types/crypto-js": "^4.2.2", "@types/jasmine": "~5.1.0", "angular-cli-ghpages": "^2.0.3", "angular-eslint": "19.3.0", "eslint": "^9.23.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "^3.5.3", "typescript": "~5.7.2", "typescript-eslint": "8.27.0"}}