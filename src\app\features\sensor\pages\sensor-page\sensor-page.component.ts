import {
  Component,
  computed,
  inject,
  linked<PERSON>ignal,
  OnD<PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { ActivatedRoute, RouterLink } from '@angular/router'
import { SensorApiService } from '../../services/api/sensor-api.service'
import { rxResource } from '@angular/core/rxjs-interop'
import { SensorTypePipe } from '../../pipes/sensor-type.pipe'
import { MeasurementsChartComponent } from '../../components/measurements-chart/measurements-chart.component'
import { MeasurementSocketService } from '../../services/ws/measurements-socket.service'
import { MeasurementUnitPipe } from '../../pipes/measurement-unit.pipe'
import { SensorsStatusService } from '../../services/ws/sensors-status.service'
import { DateUtils } from '@common/utils/date.utils'
import { DefaultFilterOptionPipe } from '../../pipes/default-filter-option.pipe'
import { DefaultFilterOption } from '../../interfaces/default-filter-option.enum'
import { generateFilterOptionDates } from './utils/generate-default-filter-option-dates'

export type NewMeasurement = {
  measurement: number
}

@Component({
  selector: 'app-sensor-page',
  imports: [
    SensorTypePipe,
    MeasurementUnitPipe,
    DefaultFilterOptionPipe,
    RouterLink,
    MeasurementsChartComponent,
  ],
  templateUrl: './sensor-page.component.html',
})
export class SensorPageComponent implements OnInit, OnDestroy {
  sensorId = inject(ActivatedRoute).snapshot.params['sensorId']

  newMeasurement = signal<NewMeasurement | null>(null)

  filterOption = signal<DefaultFilterOption>(DefaultFilterOption.TWO_HOURS)

  sensorApiService = inject(SensorApiService)

  sensorsStatusService = inject(SensorsStatusService)

  sensorsStatus = this.sensorsStatusService.sensorsStatus

  measurementService = inject(MeasurementSocketService)

  defaultFilterOptionsList = [
    DefaultFilterOption.TWO_HOURS,
    DefaultFilterOption.TODAY,
    DefaultFilterOption.ONE_WEEK,
    DefaultFilterOption.TWO_WEEKS,
  ]

  onFilterChange(event: Event): void {
    const selectedValue = (event.target as HTMLSelectElement)
      .value as DefaultFilterOption
    this.filterOption.set(selectedValue)
  }

  lastMeasurement = computed(() => {
    return this.newMeasurement() !== null
      ? this.newMeasurement()!.measurement
      : this.measurementResource.hasValue() &&
          this.measurementResource.value()!.length > 0
        ? this.measurementResource.value()![0].measurement
        : null
  })

  connectedSensors = computed(() => {
    if (this.sensorsStatus() === null) return []
    return this.sensorsStatus()!
      .filter(s => s.connected)
      .map(s => s.sensorId)
  })

  sensorResource = rxResource({
    request: () => ({
      sensorId: this.sensorId,
    }),
    loader: ({ request }) => {
      return this.sensorApiService.findOne({
        id: request.sensorId,
      })
    },
  })

  measurementResource = rxResource({
    request: () => ({
      sensorId: this.sensorId,
      filterOption: this.filterOption(),
    }),
    loader: ({ request }) => {
      const dateFilter = generateFilterOptionDates[request.filterOption]()
      return this.sensorApiService.findManyMeasurements({
        sensorId: request.sensorId,
        startDate: dateFilter.startDate,
        endDate: dateFilter.endDate,
      })
    },
  })

  ngOnInit(): void {
    this.measurementService.sendMessage('subscribe', {
      sensorId: this.sensorId,
    })
    this.measurementService.onMessage('measurement', body => {
      if (body.sensorId === this.sensorId) {
        this.newMeasurement.set({ measurement: body.measurement })
      }
    })
  }

  ngOnDestroy(): void {
    this.measurementService.sendMessage('unsubscribe', {
      sensorId: this.sensorId,
    })
  }
}

export default SensorPageComponent
