import {
  Component,
  computed,
  EventEmitter,
  inject,
  output,
  Output,
  signal,
  WritableSignal,
} from '@angular/core'
import {
  Form<PERSON>uilder,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'

@Component({
  selector: 'app-create-time-trigger-page',
  imports: [ReactiveFormsModule, FormsModule],
  templateUrl: './create-time-trigger-page.component.html',
})
export class CreateTimeTriggerPageComponent {
  days = [
    { name: 'L', value: 1 },
    { name: 'M', value: 2 },
    { name: 'X', value: 3 },
    { name: 'J', value: 4 },
    { name: 'V', value: 5 },
    { name: 'S', value: 6 },
    { name: 'D', value: 7 },
  ]
  selectedDays: { [key: string]: boolean } = {}

  hour = signal('')

  dataEvent = output<{}>()

  constructor() {
    this.days.forEach(day => {
      this.selectedDays[day.name] = false
    })
  }

  error = computed(() => {
    return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(this.hour())
      ? ''
      : 'Ingrese una hora válida'
  })
  daysError = signal('Seleccione al menos 1 día')

  getSelectedDays(): number[] {
    return this.days
      .filter(day => this.selectedDays[day.name] === true)
      .map(day => day.value)
  }

  onDayChange(day: number) {
    const sumDay =
      this.getSelectedDays().find(d => d == day) == undefined ? 1 : -1
    if (this.getSelectedDays().length + sumDay < 1) {
      this.daysError.set('Seleccione al menos 1 día')
      this.emitVoidData()
    } else {
      this.daysError.set('')
    }

    if (this.getSelectedDays().length + sumDay > 0 && this.error() == '') {
      if (sumDay == 1)
        this.emitData(true, day)
      else{
        this.emitData(false, day)
      }
    }
  }

  onHourChange() {
    if (this.daysError() == '' && this.error() == '') this.emitData()
    else this.emitVoidData()
  }

  emitData(sumDay?: boolean, day?: number) {
    this.dataEvent.emit({
      ObjectiveHour: parseInt(this.hour().split(':')[0]),
      ObjectiveMinute: parseInt(this.hour().split(':')[1]),
      ObjectiveDays: sumDay == true ? this.getSelectedDays().concat([day!]) : (sumDay == false ? this.getSelectedDays().filter(d => d != day) : this.getSelectedDays()),
    })
  }

  emitVoidData(){
    this.dataEvent.emit('');
  }
}
