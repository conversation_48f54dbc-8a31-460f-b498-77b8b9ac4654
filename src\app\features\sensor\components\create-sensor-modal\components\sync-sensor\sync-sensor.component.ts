import { Component, computed, inject, input, signal } from '@angular/core'

@Component({
  selector: 'sync-sensor',
  imports: [],
  templateUrl: './sync-sensor.component.html',
})
export class SyncSensorComponent {
  sensorToken = input.required<string>()
  sensorId = input.required<string>()
  setSyncSuccessful = input.required<(value: boolean) => void>()

  closeModal() {
    this.setSyncSuccessful()(true)
  }
}
