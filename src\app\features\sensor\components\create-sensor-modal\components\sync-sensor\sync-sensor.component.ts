import { Component, computed, inject, input, signal } from '@angular/core'
import { CommonModule } from '@angular/common'

@Component({
  selector: 'sync-sensor',
  imports: [CommonModule],
  templateUrl: './sync-sensor.component.html',
})
export class SyncSensorComponent {
  sensorToken = input.required<string>()
  sensorId = input.required<string>()
  setSyncSuccessful = input.required<(value: boolean) => void>()

  copyMessage = signal<string>('')

  closeModal() {
    this.setSyncSuccessful()(true)
  }

  async copyToClipboard(text: string, type: string) {
    try {
      await navigator.clipboard.writeText(text)
      this.copyMessage.set(`${type} copiado al portapapeles`)

      // Clear message after 3 seconds
      setTimeout(() => {
        this.copyMessage.set('')
      }, 3000)
    } catch (err) {
      console.error('Error al copiar al portapapeles:', err)
      this.copyMessage.set(`Error al copiar ${type}`)

      // Clear error message after 3 seconds
      setTimeout(() => {
        this.copyMessage.set('')
      }, 3000)
    }
  }
}
