<!-- <PERSON><PERSON>r el tamaño de la chart (ajustar el tamaño de forma responsive) -->
<div class="bg-gray-900 rounded-lg shadow-lg p-6 border border-gray-700 w-full">
  <div class="flex w-full justify-end h-0">
    <button
      class="btn btn-circle btn-error w-6 h-6"
      (click)="showConfirmModal.set(true)">
      X
    </button>
  </div>

  <div class="mb-4">
    <h3 class="text-lg font-semibold text-gray-100">
      {{ sensor().name }} - Mediciones
    </h3>
    <p class="text-sm text-gray-400">
      {{ sensor().type | sensortype }}
    </p>
  </div>
  <canvas
    baseChart
    [data]="measurementsData"
    [options]="measurementsOptions"
    [type]="'line'">
  </canvas>
</div>

<app-confirm-popup
  [message]="`¿Seguro que quiere eliminar este indicador?`"
  (confirmed)="confirmedDelete($event)"
  [show]="showConfirmModal()" />
