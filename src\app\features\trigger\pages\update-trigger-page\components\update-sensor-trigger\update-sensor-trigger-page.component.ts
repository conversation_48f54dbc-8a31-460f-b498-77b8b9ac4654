import {
  Component,
  computed,
  input,
  output,
  signal,
  WritableSignal,
  OnInit,
} from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { Sensor } from '@auth/interfaces/user'
import { AuthService } from '@auth/services/auth.service'
import { MeasurementUnitPipe } from 'src/app/features/sensor/pipes/measurement-unit.pipe'
import { ComparisonTypesEnum } from 'src/app/features/trigger/interfaces/comparison-types'
import { ComparisonPipe } from 'src/app/features/trigger/pipes/comparisons.pipe'
import { Trigger } from 'src/app/features/trigger/interfaces/trigger'
import { inject } from '@angular/core'

@Component({
  selector: 'app-update-sensor-trigger-page',
  imports: [ReactiveFormsModule, FormsModule, ComparisonPipe, MeasurementUnitPipe],
  templateUrl: './update-sensor-trigger-page.component.html',
})
export class UpdateSensorTriggerPageComponent implements OnInit {
  private authService = inject(AuthService)
  
  trigger = input.required<Trigger>()

  userSensors: WritableSignal<Sensor[]> = signal([])

  selectedSensor: WritableSignal<string> = signal('')

  selectedSensorType = computed(() => {
    const sensor = this.userSensors().find(s => s.id == this.selectedSensor())
    return sensor ? sensor.type : null
  });

  comparisons = computed(() => {
    return Object.values(ComparisonTypesEnum)
  })

  selectedComparison: WritableSignal<ComparisonTypesEnum> = signal(
    ComparisonTypesEnum.eq
  )

  sensorError = computed(() => {
    if (this.selectedSensor() != '') {
      if (
        (this.selectedComparison() == this.comparisons()[6] ||
          this.selectedComparison() == this.comparisons()[7]) &&
        this.initialMeasure() >= this.endMeasure()
      ) {
        this.emitVoidData()
        return 'Seleccione un rango válido'
      } else {
        this.emitData()
        return ''
      }
    } else {
      this.emitVoidData()
      return 'Seleccione un sensor'
    }
  })

  initialMeasure = signal(0)
  endMeasure = signal(0)

  dataEvent = output<{}>()

  ngOnInit() {
    this.userSensors.set(
      this.authService.user()?.sensors ? this.authService.user()!.sensors : []
    )

    // Initialize with current trigger data
    const currentTrigger = this.trigger()
    if (currentTrigger) {
      this.selectedSensor.set(currentTrigger.sensorId || '')
      this.selectedComparison.set(currentTrigger.comparison)
      
      if (currentTrigger.objectiveMeasures && currentTrigger.objectiveMeasures.length > 0) {
        this.initialMeasure.set(currentTrigger.objectiveMeasures[0])
        if (currentTrigger.objectiveMeasures.length > 1) {
          this.endMeasure.set(currentTrigger.objectiveMeasures[1])
        }
      }

      // Emit initial data
      this.emitData()
    }
  }

  emitData() {
    this.dataEvent.emit({
      sensorId: this.selectedSensor(),
      objectiveMeasures:
        this.selectedComparison() == this.comparisons()[6] ||
        this.selectedComparison() == this.comparisons()[7]
          ? [this.initialMeasure(), this.endMeasure()]
          : [this.initialMeasure()],
      comparison: this.selectedComparison(),
    })
  }

  emitVoidData() {
    this.dataEvent.emit('')
  }
}
