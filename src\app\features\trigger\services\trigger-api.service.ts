import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'
import { catchError, Observable, of } from 'rxjs'
import { environment } from '@environments/environment.development'
import { AuthService } from '@auth/services/auth.service'
import { CreateTriggerDTO } from './dto/create-trigger.dto'
import { CreateTriggerResponse } from './api-responses/create-trigger.response'
import { PaginationDTO } from '@common/dto/pagination.dto'
import { Pagination } from '@common/pagination/pagination.interface'
import { Trigger } from '../interfaces/trigger'
import { UpdateTriggerDTO } from './dto/update-trigger.dto'
import { OnlyResponseDTO } from '../../sensor/services/api/api-responses/only-response-dto.response'

@Injectable({ providedIn: 'root' })
export class TriggerApiService {
  private http = inject(HttpClient)

  private authService = inject(AuthService)

  create(dto: CreateTriggerDTO): Observable<CreateTriggerResponse | null> {
    return this.http
      .post<CreateTriggerResponse>(
        `${environment.baseUrl}/trigger/create-trigger`,
        dto,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error creando Trigger: ${resp}`)
          return of(null)
        })
      )
  }

  getAll(dto?: PaginationDTO): Observable<Pagination<Trigger> | null> {
    const pagination = dto?.limit ? `?limit=${dto.limit}&page=${dto.page}` : ''
    return this.http
      .get<Pagination<Trigger>>(
        `${environment.baseUrl}/trigger/many` + pagination,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error obteniendo triggers: ${resp}`)
          return of(null)
        })
      )
  }

  delete(triggerId: string): Observable<OnlyResponseDTO | null> {
    return this.http
      .delete<OnlyResponseDTO>(
        `${environment.baseUrl}/trigger/delete/${triggerId}`,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error eliminando trigger: ${resp}`)
          return of(null)
        })
      )
  }

  update(id: string, dto: UpdateTriggerDTO): Observable<OnlyResponseDTO | null> {
    return this.http
      .put<OnlyResponseDTO>(
        `${environment.baseUrl}/trigger/update/${id}`,
        dto,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error actualizando trigger: ${resp}`)
          return of(null)
        })
      )
  }
}
