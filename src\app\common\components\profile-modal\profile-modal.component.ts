import { Component, inject, signal } from '@angular/core'
import { Router } from '@angular/router'
import { AuthService } from '@auth/services/auth.service'

@Component({
  selector: 'app-profile-modal',
  imports: [],
  templateUrl: './profile-modal.component.html',
})
export class ProfileModalComponent {
  authService = inject(AuthService)

  name = signal(this.authService.user()?.name)
  email = signal(this.authService.user()?.email)

  router = inject(Router)

  isLoggingOut = signal(false)

  onLogout() {
    this.isLoggingOut.set(true)
    this.authService.logout()
    this.router.navigateByUrl('auth/login')
  }
}
