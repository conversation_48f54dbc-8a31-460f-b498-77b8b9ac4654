import { Injectable } from '@angular/core'

@Injectable({
  providedIn: 'root'
})
export class IOSFixService {

  /**
   * Detects if the current device is iOS
   */
  isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
           (navigator.userAgent.includes('Mac') && 'ontouchend' in document)
  }

  /**
   * Detects if the app is running in iOS Safari
   */
  isIOSSafari(): boolean {
    return this.isIOS() && !window.navigator.standalone
  }

  /**
   * Detects if the app is running as iOS PWA
   */
  isIOSPWA(): boolean {
    return this.isIOS() && window.navigator.standalone === true
  }

  /**
   * Forces a page reload on iOS if navigation fails
   */
  forceNavigationIfNeeded(targetUrl: string, currentUrl: string): void {
    if (this.isIOS()) {
      setTimeout(() => {
        if (window.location.pathname === currentUrl) {
          console.log('iOS navigation failed, forcing reload to:', targetUrl)
          window.location.href = targetUrl
        }
      }, 1000)
    }
  }

  /**
   * Fixes iOS viewport issues
   */
  fixIOSViewport(): void {
    if (this.isIOS()) {
      // Fix iOS viewport height issues
      const setViewportHeight = () => {
        const vh = window.innerHeight * 0.01
        document.documentElement.style.setProperty('--vh', `${vh}px`)
      }

      setViewportHeight()
      window.addEventListener('resize', setViewportHeight)
      window.addEventListener('orientationchange', () => {
        setTimeout(setViewportHeight, 500)
      })
    }
  }

  /**
   * Prevents iOS bounce effect
   */
  preventIOSBounce(): void {
    if (this.isIOS()) {
      document.addEventListener('touchmove', (e) => {
        if ((e.target as HTMLElement).closest('.scrollable')) {
          return
        }
        e.preventDefault()
      }, { passive: false })
    }
  }

  /**
   * Fixes iOS focus issues
   */
  fixIOSFocus(): void {
    if (this.isIOS()) {
      // Prevent zoom on input focus
      const inputs = document.querySelectorAll('input, select, textarea')
      inputs.forEach(input => {
        input.addEventListener('focus', () => {
          const viewport = document.querySelector('meta[name="viewport"]')
          if (viewport) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no')
          }
        })

        input.addEventListener('blur', () => {
          const viewport = document.querySelector('meta[name="viewport"]')
          if (viewport) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1, viewport-fit=cover, user-scalable=no')
          }
        })
      })
    }
  }

  /**
   * Initialize all iOS fixes
   */
  initializeIOSFixes(): void {
    if (this.isIOS()) {
      this.fixIOSViewport()
      this.preventIOSBounce()
      
      // Delay focus fixes until DOM is ready
      setTimeout(() => {
        this.fixIOSFocus()
      }, 1000)
    }
  }
}
