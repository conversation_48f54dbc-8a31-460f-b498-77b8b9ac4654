<dialog id="update_actuator_modal" class="modal" open="true">
  <div class="modal-box bg-neutral rounded-lg">
    <h3 class="font-bold text-lg mb-4">Actualizar Actuador</h3>

    <form (ngSubmit)="onSubmit()" class="space-y-4">
      <div class="form-control">
        <label class="label">
          <span class="label-text">Nombre del actuador</span>
        </label>
        <input
          type="text"
          class="input input-bordered w-full rounded-md"
          placeholder="Ingresa el nuevo nombre"
          [(ngModel)]="newName"
          name="actuatorName"
          required
          maxlength="50"
          #nameInput>
      </div>

      <div class="modal-action">
        <button
          type="button"
          class="btn btn-ghost rounded-md"
          (click)="onCancel()">
          Cancelar
        </button>
        <button
          type="submit"
          class="btn btn-primary rounded-md"
          [disabled]="!newName().trim() || newName().trim() === currentName()">
          Actualizar
        </button>
      </div>
    </form>
  </div>

  <!-- Click outside to close -->
  <form method="dialog" class="modal-backdrop">
    <button type="button" (click)="onCancel()">close</button>
  </form>
</dialog>
