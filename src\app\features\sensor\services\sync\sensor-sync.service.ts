import { inject, Injectable } from '@angular/core'
import { SyncSensorDTO } from './dto/sync.dto'
import { HttpClient } from '@angular/common/http'
import { environment } from '@environments/environment.development'
import { catchError, map, Observable, of } from 'rxjs'

type IsSynced = { isSynced: boolean }

@Injectable({ providedIn: 'root' })
export class SensorSyncService {
  private http = inject(HttpClient)

  sync(dto: SyncSensorDTO): Observable<IsSynced> {
    return this.http
      .post(`${environment.sensorServerUrl}`, null, {
        params: {
          ...dto,
        },
      })
      .pipe(
        map(() => {
          return { isSynced: true }
        }),
        catchError(resp => {
          if (resp.status === 200) {
            return of({ isSynced: true })
          }
          console.error(`Error sincronizando sensor: ${JSON.stringify(resp)}`)
          return of({ isSynced: false })
        })
      )
  }
}
