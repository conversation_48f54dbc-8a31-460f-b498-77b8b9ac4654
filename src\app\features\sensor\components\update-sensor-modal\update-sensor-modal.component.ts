import { Component, input, output, signal, OnInit } from '@angular/core'
import { FormsModule } from '@angular/forms'

@Component({
  selector: 'app-update-sensor-modal',
  imports: [FormsModule],
  templateUrl: './update-sensor-modal.component.html',
})
export class UpdateSensorModalComponent implements OnInit {
  // Inputs
  sensorId = input.required<string>()
  currentName = input.required<string>()

  // Outputs
  updateSensor = output<{ sensorId: string; name: string }>()

  cancelUpdate = output<void>()

  // Local state
  newName = signal('')

  ngOnInit() {
    // Initialize with current name when modal opens
    this.newName.set(this.currentName())
  }

  onSubmit() {
    const trimmedName = this.newName().trim()
    if (trimmedName && trimmedName !== this.currentName()) {
      this.updateSensor.emit({
        sensorId: this.sensorId(),
        name: trimmedName
      })
    }
    this.closeModal()
  }

  closeModal() {
    const modal = document.getElementById('update_sensor_modal') as HTMLDialogElement
    modal?.close()
  }

  onCancel() {
    // Reset to current name
    this.newName.set(this.currentName())
    this.cancelUpdate.emit()
    this.closeModal()
  }
}
