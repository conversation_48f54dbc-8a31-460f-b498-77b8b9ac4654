import { Injectable, inject } from '@angular/core'
import { HttpClient } from '@angular/common/http'
import { Observable, catchError, of } from 'rxjs'
import { environment } from 'src/environments/environment'
import { AuthService } from '@auth/services/auth.service'

export interface EmqxClient {
  clientid: string
  username: string
  connected: boolean
  connected_at: string
  disconnected_at?: string
  ip_address: string
  port: number
  protocol: string
}

export interface EmqxClientsResponse {
  data: EmqxClient[]
  meta: {
    count: number
    limit: number
    page: number
  }
}

@Injectable({ providedIn: 'root' })
export class EmqxApiService {
  private http = inject(HttpClient)

  private authService = inject(AuthService)

  getActiveClients(): Observable<EmqxClientsResponse | null> {
    // Create base64 encoded credentials for Basic Auth

    return this.http
      .get<EmqxClientsResponse>(`${environment.baseUrl}/common/emqx/clients/active`,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(error => {
          console.error('Error fetching EMQX clients:', error)
          return of(null)
        })
      )
  }

}
