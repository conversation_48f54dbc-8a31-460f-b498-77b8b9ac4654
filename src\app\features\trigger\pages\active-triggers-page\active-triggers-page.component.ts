import { Component, computed, inject, signal, WritableSignal } from '@angular/core'
import { RouterLink } from '@angular/router'
import { TriggerApiService } from '../../services/trigger-api.service'
import { Trigger } from '../../interfaces/trigger'
import { AuthService } from '@auth/services/auth.service'
import { ConfirmPopupComponent } from '@common/components/confirm-popup/confirm-popup.component'

@Component({
  selector: 'app-active-triggers-page',
  imports: [RouterLink, ConfirmPopupComponent],
  templateUrl: './active-triggers-page.component.html',
})
export class ActiveTriggersPageComponent {
  authService = inject(AuthService)
  triggerApiService = inject(TriggerApiService)
  query = signal('')

  userTriggers: WritableSignal<Trigger[]> = signal([])

  // Confirmation popup state
  showDeleteConfirm = signal(false)
  triggerToDelete = signal<{ id: string; name: string } | null>(null)

  deleteConfirmMessage = computed(() => {
    const trigger = this.triggerToDelete()
    return trigger
      ? `¿Estás seguro de que deseas eliminar el trigger "${trigger.name}"? Esta acción no se puede deshacer.`
      : ''
  })

  triggers = computed(() => {
    if (this.query() === '') {
      return this.userTriggers()
    }
    return this.userTriggers().filter(trigger =>
      trigger.name.toLowerCase().includes(this.query().toLowerCase())
    )
  })

  userHasTriggers = computed(() => {
    return this.triggers() !== undefined && this.triggers().length !== 0
  })

  constructor() {
    this.userTriggers.set(
      this.authService.user()?.triggers ? this.authService.user()!.triggers : []
    )
  }

  confirmDeleteTrigger(trigger: { id: string; name: string }) {
    this.triggerToDelete.set(trigger)
    this.showDeleteConfirm.set(true)
  }

  deleteTrigger(triggerId: string) {
    this.triggerApiService.delete(triggerId).subscribe({
      next: (response) => {
        if (!response) return
        // Update local state
        this.userTriggers.update(triggers =>
          triggers.filter(t => t.id !== triggerId)
        )
        // Update auth service
        this.authService.setTriggers(
          this.authService.user()!.triggers.filter(t => t.id !== triggerId)
        )
      },
      error: (error) => {
        console.error('Error deleting trigger:', error)
      }
    })
  }

  onConfirmDelete(event: boolean) {
    this.showDeleteConfirm.set(false)
    if (event === false) return
    const trigger = this.triggerToDelete()
    if (trigger) {
      this.deleteTrigger(trigger.id)
    }
    this.triggerToDelete.set(null)
  }
}
