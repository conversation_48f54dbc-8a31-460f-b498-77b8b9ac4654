import { SideMenuElement } from '../interfaces/side-menu-element.interface'

export const SideMenuElements: SideMenuElement[] = [
  {
    name: 'Dashboard',
    route: '/',
    icon: `<svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M3 13h8V3H3zm0 8h8v-6H3zm10 0h8V11h-8zm0-18v6h8V3z" />
          </svg>`,
  },
  {
    name: 'Sensores',
    route: '/sensor',
    icon: `<svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M9 4H7v2H5v12h2v2h2v-2h2V6H9zm10 4h-2V4h-2v4h-2v7h2v5h2v-5h2z" />
          </svg>`,
  },
  {
    name: 'Actuadores',
    route: '/actuator',
    icon: `<svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="24"
            height="24"
            viewBox="0 0 122.88 117.35"
            xml:space="preserve">
            <g>
              <path
                class="st0"
                d="M79.22,54.41c5.16,3.03,9.06,6.88,11.81,11.19h5.36v36.21h-5.73c-5.72,9.03-15.74,15.53-28.98,15.53 c-13.23,0-23.34-6.5-29.09-15.53h-5.96V65.6h5.91c2.98-4.48,7.25-8.48,12.96-11.58c3.04-1.64,4.88-3.69,4.9-5.53 c0.03-2.33-2.52-4.31-5.26-4.31h-1.36c-3.32,0-6.04-2.72-6.04-6.04l0,0c0-3.32,2.72-6.04,6.04-6.04h7.76l6.1-7.57 c-0.02-0.28-0.04-0.57-0.04-0.87v-9.99H32.87c-3.77,0-6.85-3.08-6.85-6.85l0,0c0-3.76,3.08-6.85,6.85-6.85h58.18 c3.76,0,6.85,3.08,6.85,6.85l0,0c0,3.77-3.08,6.85-6.85,6.85H67.51v9.99c0,0.41-0.03,0.82-0.07,1.21l5.48,7.23h6.67 c3.32,0,6.04,2.72,6.04,6.04l0,0c0,3.32-2.72,5.97-6.04,6.04c-4.57,0.1-6.25,2.06-6.04,4.34C73.91,52.73,76.83,53.01,79.22,54.41 L79.22,54.41z M101.34,65.6h4.2v-8.65c0-1.54,1.26-2.79,2.79-2.79h11.76c1.54,0,2.79,1.26,2.79,2.79v53.51 c0,1.54-1.26,2.79-2.79,2.79h-11.76c-1.54,0-2.79-1.26-2.79-2.79v-8.65h-4.2V65.6L101.34,65.6z M21.67,101.81h-4.33v8.65 c0,1.54-1.26,2.79-2.79,2.79H2.79C1.26,113.25,0,112,0,110.46V56.95c0-1.54,1.26-2.79,2.79-2.79h11.76c1.54,0,2.79,1.26,2.79,2.79 v8.65h4.33V101.81L21.67,101.81z" />
            </g>
          </svg>`,
  },
  {
    name: 'Triggers',
    route: '/trigger',
    icon: `<svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="m22 12l-4 4l-1.41-1.41L18.17 13h-5.23A8.97 8.97 0 0 1 8 20.05A3.005 3.005 0 0 1 5 23c-1.66 0-3-1.34-3-3s1.34-3 3-3c.95 0 1.78.45 2.33 1.14A6.97 6.97 0 0 0 10.91 13h-3.1C7.4 14.16 6.3 15 5 15c-1.66 0-3-1.34-3-3s1.34-3 3-3c1.3 0 2.4.84 2.82 2h3.1c-.32-2.23-1.69-4.1-3.59-5.14C6.78 6.55 5.95 7 5 7C3.34 7 2 5.66 2 4s1.34-3 3-3a2.99 2.99 0 0 1 2.99 2.95A8.97 8.97 0 0 1 12.93 11h5.23l-1.58-1.59L18 8z" />
          </svg>`,
  },
  {
    name: 'Diagramas',
    route: '/diagrams',
    icon: `<svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M19 12h-2v3h-3v2h5zM7 9h3V7H5v5h2zm14-6H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 16.01H3V4.99h18z" />
          </svg>`,
  },
]
