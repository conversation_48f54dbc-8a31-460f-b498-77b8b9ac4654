<h2 class="text-3xl font-bold mb-5">Pasos para sincronizar actuador</h2>
<ol class="list gap-2">
  <li>1. Encienda el actuador</li>
  <li>2. Conectarse en su dispositivo a la red "ActuatorWIFI"</li>
  <li>3. Inserte las credenciales de su red WiFi con acceso a internet</li>
  <li>
    4. Vuelva a conectar su dispositivo a la red WiFi con acceso a internet
  </li>
</ol>
<form [formGroup]="syncForm" (ngSubmit)="onSubmitSync()">
  <div class="flex flex-col mx-4 items-center justify-center mt-4">
    <fieldset class="fieldset">
      <label class="label" [class.hidden]="willUseCredentials()">SSID</label>
      <input
        type="text"
        class="input"
        formControlName="ssid"
        [class.hidden]="willUseCredentials()" />
      @if (syncFormUtils.isInvalidField('ssid')) {
        <span class="label-text-alt text-red-400">
          {{ syncFormUtils.getFieldError('ssid') }}
        </span>
      }
      <label class="label" [class.hidden]="willUseCredentials()"
        >Contraseña</label
      >
      <input
        type="password"
        class="input"
        [class.hidden]="willUseCredentials()"
        formControlName="password" />
      @if (syncFormUtils.isInvalidField('password')) {
        <span class="label-text-alt text-red-400">
          {{ syncFormUtils.getFieldError('password') }}
        </span>
      }
      @if (syncingError() !== '') {
        <span class="label-text-alt text-red-400">
          {{ syncingError() }}
        </span>
      }

      @if (!hasCredentials() && !hasEncryptedCredentials()) {
        <div class="flex gap-2 items-end mt-2">
          <input
            type="checkbox"
            class="checkbox checkbox-sm"
            formControlName="storeCredentials"
            (change)="onCheckStoreCredentials()" />
          <p class="text-sm text-center">Guardar credenciales</p>
        </div>

        <div class="flex gap-2 mt-2">
          <input
            type="checkbox"
            class="checkbox checkbox-sm"
            formControlName="storeEncryptedCredentials"
            (change)="onCheckStoreEncryptedCredentials()" />
          <p class="text-sm">
            Guardar credenciales encriptadas
            <label class="text-info">(recomendado)</label>
          </p>
        </div>
      }
      <!--  -->
      @else {
        <div class="flex gap-2 mt-2">
          <input
            type="checkbox"
            class="checkbox checkbox-sm"
            formControlName="useCredentials"
            (change)="onUseCredentialsCheckboxChange()" />
          <p class="text-sm">Utilizar credenciales guardadas</p>
        </div>
      }

      <fieldset
        class="flex flex-col gap-1.5 mt-0.5"
        [class.hidden]="!showEncryptionKeyField()">
        <label class="label">Clave de credenciales</label>
        <input
          type="text"
          class="input"
          formControlName="key"
          (keydown)="onChangeKeyInput()" />
        @if (syncFormUtils.isInvalidField('key')) {
          <span class="label-text-alt text-red-400">
            {{ syncFormUtils.getFieldError('key') }}
          </span>
        }
        @if (
          !syncFormUtils.isInvalidField('key') && syncFormUtils.formHasError()
        ) {
          <span class="label-text-alt text-red-400">
            {{ syncFormUtils.getFormError() }}
          </span>
        }
        @if (encryptionError() !== '') {
          <span class="label-text-alt text-red-400">
            {{ encryptionError() }}
          </span>
        }
      </fieldset>

      <div class="flex w-full gap-2 items-center justify-center mt-5">
        <button class="btn btn-primary w-30" type="submit">
          @if (!waitingForActuatorAnswer()) {
            Sincronizar
          } @else {
            <span class="loading loading-dots loading-lg"></span>
          }
        </button>
        <form method="dialog">
          <!-- if there is a button in form, it will close the modal -->
          <button
            class="btn btn-outline btn-error w-30"
            (click)="onCancelSync()">
            Cancelar
          </button>
        </form>
      </div>
    </fieldset>
  </div>
</form>
