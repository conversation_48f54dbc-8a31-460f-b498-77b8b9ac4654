<div class="max-w-4xl mx-auto p-4 rounded-lg shadow-lg">
  <h2 class="text-2xl font-bold mb-4 text-center text-gray-100">Pasos para sincronizar actuador</h2>

  <ol class="space-y-3 text-sm">
    <li class="flex items-start">
      <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3 text-xs">1</span>
      <span class="pt-0.5 text-gray-200">Encienda el actuador</span>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3 text-xs">2</span>
      <span class="pt-0.5 text-gray-200">Conectarse en su dispositivo a la red <span class="font-semibold text-blue-400">"ActuatorWIFI"</span></span>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3 text-xs">3</span>
      <span class="pt-0.5 text-gray-200">Ingrese a la siguiente dirección en su navegador: <span class="font-mono bg-gray-800 px-2 py-1 rounded text-blue-400">10.0.0.2</span></span>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3 text-xs">4</span>
      <span class="pt-0.5 text-gray-200">Ingrese los datos de su red WIFI con acceso a internet</span>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3 text-xs">5</span>
      <div class="pt-0.5 flex-1">
        <p class="mb-2 text-gray-200">Agregue los siguientes datos <span class="text-red-400 font-semibold">(Si cierra esta pestaña, los datos no se volverán a mostrar)</span>:</p>

        <div class="bg-gray-800 p-3 rounded-lg border-2 border-dashed border-gray-600 space-y-2">
          <!-- Token Section -->
          <div class="bg-gray-700 p-3 rounded-lg shadow-sm border border-gray-600">
            <label class="block text-xs font-semibold text-gray-300 mb-1">Token:</label>
            <div class="flex items-center gap-2">
              <input
                type="text"
                [value]="actuatorToken()"
                readonly
                class="flex-1 px-2 py-1.5 bg-gray-800 border border-gray-600 rounded-md font-mono text-xs text-gray-200 focus:outline-none"
              >
              <div class="relative">
                <button
                  (click)="copyToClipboard(actuatorToken(), 'Token')"
                  class="px-2 py-1.5 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200 flex items-center gap-1 text-xs"
                  title="Copiar token"
                >
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                  Copiar
                </button>
                @if (showTokenTooltip()) {
                  <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-green-600 text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap z-10">
                    ¡Copiado!
                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-green-600"></div>
                  </div>
                }
              </div>
            </div>
          </div>

          <!-- ID Section -->
          <div class="bg-gray-700 p-3 rounded-lg shadow-sm border border-gray-600">
            <label class="block text-xs font-semibold text-gray-300 mb-1">ID:</label>
            <div class="flex items-center gap-2">
              <input
                type="text"
                [value]="actuatorId()"
                readonly
                class="flex-1 px-2 py-1.5 bg-gray-800 border border-gray-600 rounded-md font-mono text-xs text-gray-200 focus:outline-none"
              >
              <div class="relative">
                <button
                  (click)="copyToClipboard(actuatorId(), 'ID')"
                  class="px-2 py-1.5 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200 flex items-center gap-1 text-xs"
                  title="Copiar ID"
                >
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                  Copiar
                </button>
                @if (showIdTooltip()) {
                  <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-green-600 text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap z-10">
                    ¡Copiado!
                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-green-600"></div>
                  </div>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3 text-xs">6</span>
      <span class="pt-0.5 text-gray-200">Presione el botón <span class="font-semibold text-green-400">"Configurar dispositivo"</span></span>
    </li>

    <li class="flex items-start">
      <span class="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3 text-xs">7</span>
      <span class="pt-0.5 text-gray-200">Vuelva a conectar su dispositivo a la red WiFi con acceso a internet</span>
    </li>
  </ol>

  <!-- Close Button Section -->
  <div class="flex justify-center mt-6 pt-4 border-t border-gray-700">
    <button
      class="px-6 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-semibold text-sm transition-colors duration-200 shadow-md hover:shadow-lg"
      (click)="closeModal()"
    >
      Cerrar
    </button>
  </div>

</div>

