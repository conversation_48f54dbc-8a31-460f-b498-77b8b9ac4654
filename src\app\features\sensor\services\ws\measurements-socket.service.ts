import { effect, inject, Injectable, linkedSignal } from '@angular/core'
import { AuthService } from '@auth/services/auth.service'
import { environment } from '@environments/environment'
import { io, Socket } from 'socket.io-client'

@Injectable({ providedIn: 'root' })
export class MeasurementSocketService {
  authService = inject(AuthService)

  token = linkedSignal(this.authService.token)

  socket: Socket | null = null

  queue: (() => void)[] = []

  private addListeners(socket: Socket): void {
    socket.on('connect', () => {
      console.log('Connected to measurement socket')
    })
    socket.on('disconnect', () => {
      console.log('Disconnected from measurement socket')
    })
  }

  private connectToSocket = effect(onCleanup => {
    if (this.token()) {
      console.log('Connecting to measurement socket', environment.measurementSocketUrl)
      this.socket = io(`${environment.measurementSocketUrl}`, {
        extraHeaders: {
          authorization: this.token() as string,
        },
      })
      this.addListeners(this.socket)
      this.queue.forEach(callback => callback())
      this.queue = []
    }

    onCleanup(() => {
      if (this.socket) {
        this.socket.disconnect()
        this.socket = null
      }
    })
  })

  sendMessage(title: string, body?: any): void {
    const messageCallback = () => {
      this.socket?.emit(title, body)
    }
    if (!this.socket) {
      this.queue.push(messageCallback)
    } else {
      messageCallback()
    }
  }

  onMessage(title: string, callback: (body: any) => void): void {
    const messageCallback = () => {
      this.socket?.on(title, callback)
    }
    if (!this.socket) {
      this.queue.push(messageCallback)
    } else {
      messageCallback()
    }
  }
}
