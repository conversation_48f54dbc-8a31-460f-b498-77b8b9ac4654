import { Component, inject, signal } from '@angular/core'
import { Router, RouterLink } from '@angular/router'
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms'
import { ErrorAlertComponent } from '@common/components/error-alert/error-alert.component'
import { emailPattern } from '@common/forms/patterns/email.pattern'
import { fullNamePattern } from '@common/forms/patterns/full-name.pattern'
import { FormGroupUtils } from '@common/forms/utils/form-group.utils'
import { CustomValidators } from '@common/forms/validators/custom-validators'
import { AuthService } from '@auth/services/auth.service'

@Component({
  selector: 'app-register-page',
  imports: [RouterLink, ReactiveFormsModule, ErrorAlertComponent],
  templateUrl: './register-page.component.html',
})
export class RegisterPageComponent {
  private fb = inject(FormBuilder)

  private router = inject(Router)

  authService = inject(AuthService)

  error = signal('')

  isPosting = signal(false)

  form = this.fb.group(
    {
      fullName: [
        '',
        [Validators.required, Validators.pattern(fullNamePattern)],
      ],
      email: ['', [Validators.required, Validators.pattern(emailPattern)]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]],
    },
    {
      validators: [CustomValidators.equalFields('password', 'confirmPassword')],
    }
  )

  formUtils = new FormGroupUtils(this.form)

  onSubmit() {
    this.form.markAllAsTouched()

    if (this.form.invalid) return

    const { fullName, email, password } = this.form.value

    this.isPosting.set(true)

    this.authService
      .register({
        email: email!,
        fullName: fullName!,
        password: password!,
      })
      .subscribe(({ isAuthenticated }) => {
        if (isAuthenticated) {
          this.router.navigateByUrl('/auth/login')
          return
        }
        this.error.set('El correo ya está siendo usado')

        this.isPosting.set(false)

        setTimeout(() => {
          this.error.set('')
        }, 2000)
      })
  }
}
