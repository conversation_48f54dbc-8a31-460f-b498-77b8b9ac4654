import { Pipe, PipeTransform } from '@angular/core'
import { ComparisonTypesEnum } from '../interfaces/comparison-types'

const comparisonTranslator: Map<string, string> = new Map([
  [ComparisonTypesEnum.eq, 'Igual'],
  [ComparisonTypesEnum.g, 'Mayor'],
  [ComparisonTypesEnum.ge, 'Mayor o igual'],
  [ComparisonTypesEnum.l, 'Menor'],
  [ComparisonTypesEnum.le, 'Menor o igual'],
  [ComparisonTypesEnum.ne, 'Diferente'],
  [ComparisonTypesEnum.in, 'Den<PERSON>'],
  [ComparisonTypesEnum.out, 'Fuera'],
])

@Pipe({
  name: 'comparisonPipe',
})
export class ComparisonPipe implements PipeTransform {
  transform(value: string): string {
    return comparisonTranslator.get(value) ?? 'Comparación desconocida'
  }
}
