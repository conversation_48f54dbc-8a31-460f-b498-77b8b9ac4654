import { Component, inject, signal } from '@angular/core'
import { Form<PERSON>uilder, ReactiveFormsModule, Validators } from '@angular/forms'
import { Router, RouterLink } from '@angular/router'
import { ErrorAlertComponent } from '@common/components/error-alert/error-alert.component'
import { emailPattern } from '@common/forms/patterns/email.pattern'
import { FormGroupUtils } from '@common/forms/utils/form-group.utils'
import { AuthService } from '@auth/services/auth.service'
import { NotificationService } from '@common/services/notification.service'
import { IOSFixService } from '@common/services/ios-fix.service'

@Component({
  selector: 'app-login-page',
  imports: [RouterLink, ReactiveFormsModule, ErrorAlertComponent],
  templateUrl: './login-page.component.html',
})
export class LoginPageComponent {
  private fb = inject(FormBuilder)

  private router = inject(Router)

  authService = inject(AuthService)

  notificationService = inject(NotificationService)
  iosFixService = inject(IOSFixService)

  error = signal('')

  isPosting = signal(false)

  form = this.fb.group({
    email: ['', [Validators.required, Validators.pattern(emailPattern)]],
    password: ['', [Validators.required, Validators.minLength(8)]],
  })

  formUtils = new FormGroupUtils(this.form)



  async onSubmit() {
    this.form.markAllAsTouched()

    if (this.form.invalid) return

    const { email, password } = this.form.value

    this.isPosting.set(true)
    this.authService
      .login({
        email: email!.toLocaleLowerCase(),
        password: password!,
      })
      .subscribe({
        next: ({ isAuthenticated }) => {
          if (isAuthenticated) {
            this.notificationService.saveNotificationToken()

            // iOS-specific fix: Use setTimeout to ensure proper navigation
            setTimeout(() => {
              this.router.navigateByUrl('/').then(() => {
                // Force reload on iOS if navigation doesn't work
                this.iosFixService.forceNavigationIfNeeded('/', '/auth/login')
              })
            }, 100)
            return
          }
          this.error.set('Credenciales incorrectas')
          this.isPosting.set(false)

          setTimeout(() => {
            this.error.set('')
          }, 2000)
        },
        error: (error) => {
          console.error('Login error:', error)
          this.error.set('Error de conexión')
          this.isPosting.set(false)

          setTimeout(() => {
            this.error.set('')
          }, 2000)
        }
      })
  }
}
