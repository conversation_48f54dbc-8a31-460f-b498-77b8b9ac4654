<div class="flex gap-5 mb-10 flex-wrap justify-between">
  <div class="flex gap-5">
    <h2 class="text-3xl font-bold">Dashboard</h2>
  </div>
  <div class="w-fit flex justify-center">
    <select class="select w-fit" (change)="onFilterChange($event)">
      @for (option of defaultFilterOptionsList; track option) {
        <option [value]="option" [selected]="filterOption() === option">
          {{ option | deffilteroption }}
        </option>
      }
    </select>
  </div>
  <div class="flex w-fit justify-center gap-5">
    <button
      class="btn btn-primary rounded-md"
      onclick="create_dashboard_modal.showModal()">
      +
    </button>
  </div>
</div>
@if (
  DashboardResources.hasValue() &&
  SensorData().length == DashboardResources.value().length
) {
  @if (DashboardResources.value().length > 0) {
    <div
      class="grid grid-cols-1 md:grid-cols-2 sm:grid-cols-1 xs:grid-cols-1 gap-2">
      @for (item of DashboardResources.value(); track $index) {
        @if (item.type == dashboardTypes.MEASURE) {
          <dashboard-chart
            [sensor]="SensorData()[$index]"
            [measurements]="item.measurements.reverse()"
            [id]="item.id"
            (deleted)="onChangedDashboard($event)" />
        } @else {
          <single-measure-chart
            [sensor]="SensorData()[$index]"
            [measurements]="toSignal(item.measurements)"
            [type]="item.type"
            [id]="item.id"
            (deleted)="onChangedDashboard($event)" />
        }
      }
    </div>
  } @else {
    <!-- No dashboards message -->
    <div class="flex w-full justify-center items-center h-70">
      <div class="card w-96 bg-neutral card-md shadow-sm rounded-2xl">
        <div class="card-body flex flex-col justify-center items-center">
          <p class="text-2xl font-semibold">Aún no tienes ningún dashboard</p>
          <p
            class="text-2xl font-semibold underline cursor-pointer"
            onclick="create_dashboard_modal.showModal()">
            ¡Crea uno!
          </p>
        </div>
      </div>
    </div>
  }
} @else if (DashboardResources.isLoading()) {
  <!-- Loading spinner -->
  <div class="flex justify-center items-start pt-32 h-screen w-full">
    <span class="loading loading-spinner w-20 h-20 text-primary"></span>
  </div>
}
<app-create-dashboard-modal (created)="onChangedDashboard($event)" />
