import { Pipe, PipeTransform } from '@angular/core'
import { ActuatorType } from '../interfaces/actuator'

export const actuatorTypeTranslator: Map<string, string> = new Map([
  [ActuatorType.PUMP, 'Bomba de Agua'],
  [ActuatorType.VALVE, 'Válvula'],
])

@Pipe({
  name: 'actuatortype',
})
export class ActuatorTypePipe implements PipeTransform {
  transform(value: string): string {
    return actuatorTypeTranslator.get(value) ?? 'Tipo desconocido'
  }
}
