import { Injectable, signal } from '@angular/core'
import { CipherUtils } from '@common/utils/cypher.utils'

const ENCRYPTED_CREDENTIALS = 'encrypted-credentials'
const CREDENTIALS = 'credentials'

type WifiCredentials = {
  ssid: string
  password: string
}

@Injectable({ providedIn: 'root' })
export class WiFiCredentialsService {
  hasCredentials = signal(localStorage.getItem(CREDENTIALS) !== null)
  hasEncryptedCredentials = signal(
    localStorage.getItem(ENCRYPTED_CREDENTIALS) !== null
  )

  storeCredentials(credentials: WifiCredentials) {
    localStorage.setItem(CREDENTIALS, JSON.stringify(credentials))
    this.hasCredentials.set(true)
    this.hasEncryptedCredentials.set(false)
  }

  storeEncryptedCredentials(credentials: WifiCredentials, key: string) {
    const ciphered = CipherUtils.encrypt(credentials, key)
    localStorage.setItem(ENCRYPTED_CREDENTIALS, ciphered)
    this.hasEncryptedCredentials.set(true)
    this.hasCredentials.set(false)
  }

  getCredentials(): WifiCredentials | null {
    const credentials = localStorage.getItem(CREDENTIALS)
    return credentials ? JSON.parse(credentials) : null
  }

  decryptCredentials(key: string): WifiCredentials | null {
    const encryptedCredentials = localStorage.getItem(ENCRYPTED_CREDENTIALS)
    const decryptedCredentials = CipherUtils.decrypt(key, encryptedCredentials!)
    return decryptedCredentials !== '' ? JSON.parse(decryptedCredentials) : null
  }

  clearCredentials() {
    localStorage.removeItem(CREDENTIALS)
    localStorage.removeItem(ENCRYPTED_CREDENTIALS)
    this.hasCredentials.set(false)
    this.hasEncryptedCredentials.set(false)
  }
}
