import { Component, inject, ResourceRef, signal } from '@angular/core'
import { rxResource } from '@angular/core/rxjs-interop'
import { DashboardApiService } from '../../services/api/dashboard-api.service'
import { SensorApiService } from 'src/app/features/sensor/services/api/sensor-api.service'
import { generateFilterOptionDates } from 'src/app/features/sensor/pages/sensor-page/utils/generate-default-filter-option-dates'
import { DefaultFilterOption } from 'src/app/features/sensor/interfaces/default-filter-option.enum'
import { tap } from 'rxjs'
import { FindManyDashboardsResponse } from '../../services/api/api-responses/find-many-dashboards.response'
import { Sensor } from '@auth/interfaces/user'
import { MeasurementSocketService } from 'src/app/features/sensor/services/ws/measurements-socket.service'
import { DashboardChartComponent } from '../../components/dashboard-chart/dashboard-chart.component'
import { DashboardTypesEnum } from '../../interfaces/dashboard-types.enum'
import { RouterLink } from '@angular/router'
import { CreateDashboardModalComponent } from '../../components/create-dashboard-modal/create-dashboard-modal.component'
import { SingleMeasureChartComponent } from '../../components/single-measure-chart/single-measure-chart.component'
import { DefaultFilterOptionPipe } from 'src/app/features/sensor/pipes/default-filter-option.pipe'

export type NewMeasurement = {
  measurement: number
}
@Component({
  selector: 'app-dashboard-page',
  imports: [
    DashboardChartComponent,
    SingleMeasureChartComponent,
    CreateDashboardModalComponent,
    DefaultFilterOptionPipe,
  ],
  templateUrl: './dashboard-page.component.html',
})
export class DashboardPageComponent {
  sensorApiService = inject(SensorApiService)
  dashboardApiService = inject(DashboardApiService)

  dashboardTypes = DashboardTypesEnum
  newMeasurement = signal<(NewMeasurement | null)[]>([
    null,
    null,
    null,
    null,
    null,
    null,
  ])
  SensorData = signal<Sensor[]>([])
  constructor() {}

  filterOption = signal<DefaultFilterOption>(DefaultFilterOption.TWO_HOURS)
  defaultFilterOptionsList = [
    DefaultFilterOption.TWO_HOURS,
    DefaultFilterOption.TODAY,
    DefaultFilterOption.ONE_WEEK,
    DefaultFilterOption.TWO_WEEKS,
  ]
  onFilterChange(event: Event): void {
    const selectedValue = (event.target as HTMLSelectElement)
    .value as DefaultFilterOption
    this.filterOption.set(selectedValue)
  }

  DashboardResources = rxResource({
    request: () => ({
      filterOption: this.filterOption(),
    }),
    loader: ({ request }) => {
      const dateFilter = generateFilterOptionDates[request.filterOption]()
      return this.dashboardApiService
        .findManyDashboards({
          startDate: dateFilter.startDate,
          endDate: dateFilter.endDate,
        })
        .pipe(
          tap(data => {
            this.fetchSensorData(data)
          })
        )
    },
  })

  async fetchSensorData(data: FindManyDashboardsResponse[]) {
    try {
      this.SensorData.set([])
      for (const dashboard of data) {
        this.sensorApiService
          .findOne({ id: dashboard.sensorId })
          .subscribe(data => {
            this.SensorData.update(prev => {
              prev.push(data!)
              return prev
            })
          })
      }
    } catch (error) {
      console.error('Error:', error)
    }
  }

  toSignal<T>(data: T) {
    return signal<T>(data)
  }

  onChangedDashboard(created: boolean) {
    if (created) {
      this.DashboardResources.reload()
    }
  }
}
