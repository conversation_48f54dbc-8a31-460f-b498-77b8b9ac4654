import {
  Component,
  computed,
  inject,
  signal,
  WritableSignal,
  OnInit,
} from '@angular/core'
import {
  Form<PERSON>uilder,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { Router, RouterLink } from '@angular/router'
import { ErrorAlertComponent } from '@common/components/error-alert/error-alert.component'
import { FormGroupUtils } from '@common/forms/utils/form-group.utils'
import { Actuator } from 'src/app/features/actuators/interfaces/actuator'
import { Action, ActuatorAction } from '../../interfaces/actuator-action'
import { ActionPipe } from '../../pipes/actions.pipe'
import { TriggerApiService } from '../../services/trigger-api.service'
import { AuthService } from '@auth/services/auth.service'
import { UpdateTimeTriggerPageComponent } from './components/update-time-trigger/update-time-trigger-page.component'
import { UpdateSensorTriggerPageComponent } from './components/update-sensor-trigger/update-sensor-trigger-page.component'
import { Trigger } from '../../interfaces/trigger'
import { UpdateTriggerDTO } from '../../services/dto/update-trigger.dto'

@Component({
  selector: 'app-update-trigger-page',
  imports: [
    RouterLink,
    ReactiveFormsModule,
    ErrorAlertComponent,
    FormsModule,
    ActionPipe,
    UpdateTimeTriggerPageComponent,
    UpdateSensorTriggerPageComponent
  ],
  templateUrl: './update-trigger-page.component.html',
})
export class UpdateTriggerPageComponent implements OnInit {
  private authService = inject(AuthService)
  private triggerService = inject(TriggerApiService)
  private router = inject(Router)
  private fb = inject(FormBuilder)

  // Get trigger from URL
  trigger = computed(() => {
    return this.authService
      .user()
      ?.triggers.find(t => t.id == this.router.url.split('/')[3])
  })

  userActuators: WritableSignal<Actuator[]> = signal([])

  actions = computed(() => {
    return Object.values(Action)
  })

  actuatorActions: WritableSignal<ActuatorAction[]> = signal([])

  selectedActuator?: string = undefined
  selectedAction?: Action = undefined

  triggerType = computed(() => {
    return this.trigger()?.objectiveHour ? 'time' : 'sensor'
  })

  data: WritableSignal<any> = signal('')
  error = signal('')
  actuatorError = signal('')

  actuatorActionError = computed(() => {
    return this.actuatorActions().length > 0
      ? ''
      : 'Seleccione al menos 1 actuador'
  })

  isUpdating = signal(false)

  form = this.fb.group({
    name: ['', [Validators.required]],
  })

  formUtils = new FormGroupUtils(this.form)

  ngOnInit() {
    this.userActuators.set(
      this.authService.user()?.actuators ? this.authService.user()!.actuators : []
    )

    // Initialize form with current trigger data
    const currentTrigger = this.trigger()
    if (currentTrigger) {
      this.form.patchValue({
        name: currentTrigger.name
      })

      // Initialize actuator actions
      const initialActuatorActions = currentTrigger.actuators.map(actuator => ({
        actuatorId: actuator.actuatorId,
        actuatorName: this.userActuators().find(a => a.id === actuator.actuatorId)?.name || '',
        action: actuator.action
      }))
      this.actuatorActions.set(initialActuatorActions)
    }
  }

  onActuatorChange() {
    this.actuatorError.set('')
  }

  onActuatorAdd() {
    if (!this.selectedActuator || !this.selectedAction) {
      this.actuatorError.set('No deben estar vacíos')
      return
    }
    if (
      this.actuatorActions().find(
        actuator => actuator.actuatorId == this.selectedActuator
      )
    ) {
      this.actuatorError.set('No puede existir 2 veces el mismo actuador')
      return
    }
    this.actuatorActions.set(
      this.actuatorActions().concat({
        actuatorId: this.selectedActuator,
        actuatorName: this.userActuators().find(
          actuator => actuator.id == this.selectedActuator
        )!.name,
        action: this.selectedAction!,
      })
    )
  }

  removeActuator(id: string) {
    this.actuatorActions.set(
      this.actuatorActions().filter(actuator => actuator.actuatorId != id)
    )
    this.onActuatorChange()
  }

  onSubmit() {
    let valid = true
    this.form.markAllAsTouched()
    if (this.form.invalid) valid = false
    if (this.data() == '') valid = false
    if (this.actuatorActions().length < 1) valid = false

    if (!valid) {
      this.error.set('Complete todos los campos')
      setTimeout(() => {
        this.error.set('')
      }, 2000)
      return
    }

    const currentTrigger = this.trigger()
    if (!currentTrigger) return

    this.error.set('')
    this.isUpdating.set(true)

    const updateData: UpdateTriggerDTO = {
      name: this.form.value.name!,
      comparison: this.triggerType() === 'time' ? '>' : this.data().comparison,
      actuators: this.actuatorActions(),
      ...this.data(),
    }
    this.triggerService
      .update(currentTrigger.id, updateData)
      .subscribe({
        next: (response) => {
          if (!response) {
            this.error.set('Error al actualizar el trigger')
            setTimeout(() => {
              this.error.set('')
            }, 2000)
            this.isUpdating.set(false)
            return
          }

          // Update auth service state
          this.authService.setTriggers(
            this.authService.user()!.triggers.map(t => {
              if (t.id === currentTrigger.id) {
                return {
                  ...t,
                  name: updateData.name,
                  comparison: updateData.comparison as any,
                  actuators: updateData.actuators,
                  sensorId: updateData.sensorId,
                  objectiveMeasures: updateData.objectiveMeasures,
                  objectiveHour: this.triggerType() === 'time'
                    ? `0 ${updateData.ObjectiveMinute} ${updateData.ObjectiveHour} * * ${updateData.ObjectiveDays?.join(',')}`
                    : undefined
                }
              }
              return t
            })
          )

          this.isUpdating.set(false)
          this.router.navigateByUrl('/trigger')
        },
        error: (error) => {
          this.error.set('Error al actualizar el trigger')
          setTimeout(() => {
            this.error.set('')
          }, 2000)
          this.isUpdating.set(false)
        }
      })
  }

  getData(data: any) {
    this.data.set(data)
  }
}
