import {
  Component,
  computed,
  inject,
  signal,
  WritableSignal,
} from '@angular/core'
import {
  Form<PERSON>uilder,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { Router, RouterLink } from '@angular/router'
import { ErrorAlertComponent } from '@common/components/error-alert/error-alert.component'
import { FormGroupUtils } from '@common/forms/utils/form-group.utils'
import { Actuator } from 'src/app/features/actuators/interfaces/actuator'
import { Action, ActuatorAction } from '../../interfaces/actuator-action'
import { ActionPipe } from '../../pipes/actions.pipe'
import { TriggerApiService } from '../../services/trigger-api.service'
import { CreateTimeTriggerPageComponent } from './components/create-time-trigger/create-time-trigger-page.component'
import { AuthService } from '@auth/services/auth.service'
import { CreateSensorTriggerPageComponent } from './components/create-sensor-trigger/create-sensor-trigger-page.component'

@Component({
  selector: 'app-create-trigger-page',
  imports: [
    RouterLink,
    ReactiveFormsModule,
    ErrorAlertComponent,
    FormsModule,
    ActionPipe,
    CreateTimeTriggerPageComponent,
    CreateSensorTriggerPageComponent
  ],
  templateUrl: './create-trigger-page.component.html',
})
export class CreateTriggerPageComponent {
  private authService = inject(AuthService)
  private triggerService = inject(TriggerApiService)

  userActuators: WritableSignal<Actuator[]> = signal([])

  actions = computed(() => {
    return Object.values(Action)
  })

  actuactorActions: WritableSignal<ActuatorAction[]> = signal([])

  selectedActuator?: string = undefined

  selectedAction?: Action = undefined

  triggerType = signal('time')
  constructor() {
      this.userActuators.set(
        this.authService.user()?.actuators ? this.authService.user()!.actuators : []
      )
  }

  data: WritableSignal<any> = signal('')

  private fb = inject(FormBuilder)

  error = signal('')

  private router = inject(Router)

  actuatorError = signal('')
  actuatorActionError = computed(() => {
    return this.actuactorActions().length > 0
      ? ''
      : 'Seleccione al menos 1 actuador'
  })

  isCreating = signal(false)

  form = this.fb.group({
    name: ['', [Validators.required]],
  })

  formUtils = new FormGroupUtils(this.form)

  onActuatorChange() {
    this.actuatorError.set('')
  }

  onActuatorAdd() {
    if (!this.selectedActuator || !this.selectedAction) {
      this.actuatorError.set('No deben estar vacíos')
      return
    }
    if (
      this.actuactorActions().find(
        actuator => actuator.actuatorId == this.selectedActuator
      )
    ) {
      this.actuatorError.set('No puede existir 2 veces el mismo actuador')
      return
    }
    this.actuactorActions.set(
      this.actuactorActions().concat({
        actuatorId: this.selectedActuator,
        actuatorName: this.userActuators().find(
          actuator => actuator.id == this.selectedActuator
        )!.name,
        action: this.selectedAction!,
      })
    )
  }

  removeActuator(id: string) {
    this.actuactorActions.set(
      this.actuactorActions().filter(actuator => actuator.actuatorId != id)
    )
    this.onActuatorChange()
  }

  onSubmit() {
    let valid = true
    this.form.markAllAsTouched()
    if (this.form.invalid) valid = false
    if (this.data() == '') valid = false
    if (this.actuactorActions().length < 1) valid = false
    if (!valid) {
      this.error.set('Complete todos los campos')
      setTimeout(() => {
        this.error.set('')
      }, 2000)
      return
    }
    this.error.set('')
    this.isCreating.set(true)
    this.triggerService
      .create({
        name: this.form.value.name!,
        comparison: '>',
        actuators: this.actuactorActions(),
        ...this.data(),
      })
      .subscribe(response => {
        if (!response) {
          this.error.set('Credenciales incorrectas')
          setTimeout(() => {
            this.error.set('')
          }, 2000)
        }
        this.isCreating.set(false)
        this.router.navigateByUrl('/trigger')
      })
  }

  getData(data: any) {
    this.data.set(data)
  }

  onClickTriggerType(type: string) {
    this.triggerType.set(type)
    this.data.set('')
  }
}
