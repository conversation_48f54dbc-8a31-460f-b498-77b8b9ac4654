import {
  Component,
  ElementRef,
  OnInit,
  signal,
  viewChild,
  input,
} from '@angular/core'

@Component({
  selector: 'sync-success-message',
  imports: [],
  templateUrl: './sync-success-message.component.html',
})
export class SyncSuccessMessageComponent implements OnInit {
  setSyncSuccessful = input.required<(value: boolean) => void>()
  setIsSyncing = input.required<(value: boolean) => void>()

  hiddenCancelButton =
    viewChild.required<ElementRef<HTMLButtonElement>>('cancelButton')

  isClosingAfterSuccess = signal(false)

  ngOnInit(): void {
    setTimeout(() => {
      this.isClosingAfterSuccess.set(true)
      const hiddenCancelButton = this.hiddenCancelButton().nativeElement
      hiddenCancelButton.click()
      this.isClosingAfterSuccess.set(false)
    }, 2300)
    setTimeout(() => {
      this.setSyncSuccessful()(false)
      this.setIsSyncing()(false)
    }, 2600)
  }
}
