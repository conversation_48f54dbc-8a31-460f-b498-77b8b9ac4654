import { Actuator } from "src/app/features/actuators/interfaces/actuator"
import { Trigger } from "src/app/features/trigger/interfaces/trigger"

export interface User {
  id: string
  email: string
  name: string
  sensors: Sensor[]
  actuators: Actuator[]
  triggers: Trigger[]
}

export interface Sensor {
  id: string
  name: string
  type: SensorType
}

export enum SensorType {
  LEVEL = 'waterLevel',
  PRESSURE = 'pressure',
  PH = 'ph',
  TDS = 'tds',
}
