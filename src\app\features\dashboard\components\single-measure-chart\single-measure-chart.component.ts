import {
  Component,
  input,
  computed,
  signal,
  output,
  inject,
  Signal,
  effect,
  OnInit,
  OnDestroy,
} from '@angular/core'
import { Sensor } from '@auth/interfaces/user'
import { FindManyMeasurementsResponse } from 'src/app/features/sensor/services/api/api-responses/find-many-measurements.response'
import { DashboardTypesEnum } from '../../interfaces/dashboard-types.enum'
import { MeasurementUnitPipe } from 'src/app/features/sensor/pipes/measurement-unit.pipe'
import { SensorTypePipe } from 'src/app/features/sensor/pipes/sensor-type.pipe'
import { DatePipe } from '@angular/common'
import { DashboardApiService } from '../../services/api/dashboard-api.service'
import { ConfirmPopupComponent } from '@common/components/confirm-popup/confirm-popup.component'
import { NewMeasurement } from '../../pages/dashboard-page/dashboard-page.component'
import { MeasurementSocketService } from 'src/app/features/sensor/services/ws/measurements-socket.service'

@Component({
  selector: 'single-measure-chart',
  imports: [MeasurementUnitPipe, SensorTypePipe, DatePipe, ConfirmPopupComponent],
  templateUrl: './single-measure-chart.component.html',
})
export class SingleMeasureChartComponent implements OnInit, OnDestroy {

  showConfirmModal = signal<boolean>(false)
  deleted = output<boolean>()
  dashboardApiService = inject(DashboardApiService)
  measurementService = inject(MeasurementSocketService)

  id = input.required<string>()
  sensor = input.required<Sensor>()
  measurements = input.required<Signal<FindManyMeasurementsResponse[]>>()
  type = input.required<DashboardTypesEnum>()

  newMeasurement = signal<NewMeasurement | null>(null)
  private onNewMeasurementEffect = effect(() => {
    if (this.newMeasurement() !== null) {
      this.measurements()().push({
        measurement: this.newMeasurement()?.measurement!,
        timestamp: new Date(),
      })
    }
  })

  // Computed property to get the chart type display name
  chartTypeDisplayName = computed(() => {
    const typeMap = {
      [DashboardTypesEnum.MEASURE]: 'Medición',
      [DashboardTypesEnum.AVERAGE]: 'Promedio',
      [DashboardTypesEnum.MAXIMUM]: 'Máximo',
      [DashboardTypesEnum.MINIMUM]: 'Mínimo',
    }
    return typeMap[this.type()]
  })

  finalMeasure = signal<{ measurement: number; timestamp: Date }>({
    measurement: 0,
    timestamp: new Date(),
  })

  private calculateFinalMeasure = effect(()=> {
    if (this.measurements()().length === 0) {
      this.finalMeasure.set({
        measurement: 0,
        timestamp: new Date()
      })
      return
    }
    switch (this.type()){
      case DashboardTypesEnum.AVERAGE:{
        let average = this.measurements()().reduce((acc, curr) => acc + curr.measurement, 0) / this.measurements()().length
        this.finalMeasure.set({
          measurement: parseFloat(average.toFixed(2)),
          timestamp: this.measurements()()[this.measurements()().length - 1].timestamp
        })
        break
      }
      case DashboardTypesEnum.MAXIMUM:{
        this.finalMeasure.set({
          measurement: Math.max(...this.measurements()().map(m => m.measurement)),
          timestamp: this.measurements()()[this.measurements()().map(m => m.measurement).indexOf(Math.max(...this.measurements()().map(m => m.measurement)))].timestamp
        })
        break
      }
      case DashboardTypesEnum.MINIMUM:
        this.finalMeasure.set({
          measurement: Math.min(...this.measurements()().map(m => m.measurement)),
          timestamp: this.measurements()()[this.measurements()().map(m => m.measurement).indexOf(Math.min(...this.measurements()().map(m => m.measurement)))].timestamp
        })
        break
      default:
        this.finalMeasure.set({
          measurement: 0,
          timestamp: new Date()
        })
    }
  })

  confirmedDelete (){
    this.dashboardApiService.delete(this.id()).subscribe((response)=> {
      this.showConfirmModal.set(false)
      this.deleted.emit(true)
    })
  }

  ngOnInit(): void {
    this.measurementService.sendMessage('subscribe', {
      sensorId: this.sensor().id,
    })
    this.measurementService.onMessage('measurement', body => {
      if (body.sensorId === this.sensor().id) {
        this.newMeasurement.set({ measurement: body.measurement })
      }
    })
  }

  ngOnDestroy(): void {
    this.measurementService.sendMessage('unsubscribe', {
      sensorId: this.sensor().id,
    })
  }
}
