import { Component, input, output } from '@angular/core'

@Component({
  selector: 'app-confirm-popup',
  imports: [],
  templateUrl: './confirm-popup.component.html',
})
export class ConfirmPopupComponent {
  message = input.required<string>()
  show = input.required<boolean>()
  confirmed = output<boolean>()

  executeCallback (event: MouseEvent){
    event.preventDefault()

    this.confirmed.emit(true)
  }
}
