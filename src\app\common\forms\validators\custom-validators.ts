import { AbstractControl } from '@angular/forms'

export class CustomValidators {
  static equalFields(fieldOne: string, fieldTwo: string) {
    return (formGroup: AbstractControl) => {
      const field1Value = formGroup.get(fieldOne)?.value
      const field2Value = formGroup.get(fieldTwo)?.value

      return field1Value === field2Value ? null : { fieldsNotEqual: true }
    }
  }
  static requiredFieldTwoIfFieldOne(fieldOne: string, fieldTwo: string) {
    return (formGroup: AbstractControl) => {
      const field1Value = formGroup.get(fieldOne)?.value
      const field2Value = formGroup.get(fieldTwo)?.value

      const result =
        field1Value && !field2Value
          ? {
              requiredNotFilled: {
                requiredField: fieldTwo,
              },
            }
          : null

      return result
    }
  }
}
