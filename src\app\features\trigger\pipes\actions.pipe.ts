import { Pipe, PipeTransform } from '@angular/core'
import { Action } from '../interfaces/actuator-action'

const actionTranslator: Map<string, string> = new Map([
  [Action.OPEN, 'Abrir'],
  [Action.CLOSE, 'Cerrar']
])

@Pipe({
  name: 'actionPipe',
})
export class ActionPipe implements PipeTransform {
  transform(value: string): string {
    return actionTranslator.get(value) ?? 'Acción desconocida'
  }
}
