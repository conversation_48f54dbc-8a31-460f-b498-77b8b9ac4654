import { Pipe, PipeTransform } from '@angular/core'
import { DefaultFilterOption } from '../interfaces/default-filter-option.enum'

export const defaultFilterOptionTranslator: Record<
  DefaultFilterOption,
  string
> = {
  [DefaultFilterOption.TWO_HOURS]: 'Últimas 2 horas',
  [DefaultFilterOption.TODAY]: 'Hoy',
  [DefaultFilterOption.ONE_WEEK]: 'Una semana',
  [DefaultFilterOption.TWO_WEEKS]: 'Dos semanas',
}

@Pipe({
  name: 'deffilteroption',
})
export class DefaultFilterOptionPipe implements PipeTransform {
  transform(value: DefaultFilterOption): string {
    return defaultFilterOptionTranslator[value]
  }
}
