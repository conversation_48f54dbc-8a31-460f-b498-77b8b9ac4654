import { DateUtils } from '@common/utils/date.utils'
import { DefaultFilterOption } from '../../../interfaces/default-filter-option.enum'

export const generateFilterOptionDates: Record<
  DefaultFilterOption,
  () => { startDate: Date; endDate: Date }
> = {
  [DefaultFilterOption.TWO_HOURS]: function (): {
    startDate: Date
    endDate: Date
  } {
    const currentDate = new Date()
    const twoHoursAgo = DateUtils.subtractHours(currentDate, 2)
    return {
      startDate: twoHoursAgo,
      endDate: currentDate,
    }
  },
  [DefaultFilterOption.TODAY]: function (): {
    startDate: Date
    endDate: Date
  } {
    const currentDate = new Date()
    const oneDayAgo = DateUtils.subtractDays(currentDate, 1)
    return {
      startDate: oneDayAgo,
      endDate: currentDate,
    }
  },
  [DefaultFilterOption.ONE_WEEK]: function (): {
    startDate: Date
    endDate: Date
  } {
    const currentDate = new Date()
    const oneWeekAgo = DateUtils.subtractWeeks(currentDate, 1)
    return {
      startDate: oneWeekAgo,
      endDate: currentDate,
    }
  },
  [DefaultFilterOption.TWO_WEEKS]: function (): {
    startDate: Date
    endDate: Date
  } {
    const currentDate = new Date()
    const twoWeeksAgo = DateUtils.subtractWeeks(currentDate, 2)
    return {
      startDate: twoWeeksAgo,
      endDate: currentDate,
    }
  },
}
