<form autocomplete="off" [formGroup]="form" (ngSubmit)="onSubmit()">
  <fieldset class="fieldset">
    <label class="label">Nombre</label>
    <input type="text" class="input" formControlName="fullName" />
    @if (formUtils.isInvalidField('fullName')) {
      <span class="label-text-alt text-red-400">
        {{ formUtils.getFieldError('fullName') }}
      </span>
    }
    <label class="label">Correo electónico</label>
    <input type="text" class="input" formControlName="email" />
    @if (formUtils.isInvalidField('email')) {
      <span class="label-text-alt text-red-400">
        {{ formUtils.getFieldError('email') }}
      </span>
    }
    <label class="label">Contraseña</label>
    <input type="password" class="input" formControlName="password" />
    @if (formUtils.isInvalidField('password')) {
      <span class="label-text-alt text-red-400">
        {{ formUtils.getFieldError('password') }}
      </span>
    }
    <label class="label">Confirmar contraseña</label>
    <input type="password" class="input" formControlName="confirmPassword" />
    @if (formUtils.isInvalidField('confirmPassword')) {
      <span class="label-text-alt text-red-400">
        {{ formUtils.getFieldError('confirmPassword') }}
      </span>
    }
    @if (
      !formUtils.isInvalidField('confirmPassword') && formUtils.formHasError()
    ) {
      <span class="label-text-alt text-red-400">
        {{ formUtils.getFormError() }}
      </span>
    }

    <div class="w-full flex justify-center">
      <button class="btn btn-primary mt-4 w-30" type="submit">
        @if (!isPosting()) {
          Registrar
        } @else {
          <span class="loading loading-dots loading-lg"></span>
        }
      </button>
    </div>

    <div class="flex justify-center items-center mt-2">
      <a class="link link-hover" routerLink="/auth/login"
        >¿Ya tienes cuenta? Inicia sesión</a
      >
    </div>
  </fieldset>
</form>

@if (error() !== '') {
  <app-error-alert [message]="error()" />
}
