import { Component, inject } from '@angular/core'
import { RouterLink } from '@angular/router'
import { DomSanitizer } from '@angular/platform-browser'
import { SideMenuElements } from './constants/side-menu-elements'

@Component({
  selector: 'app-side-menu',
  imports: [RouterLink],
  templateUrl: './side-menu.component.html',
})
export class SideMenuComponent {
  private sanitizer = inject(DomSanitizer)

  sideMenuElements = SideMenuElements.map(element => ({
    ...element,
    safeIcon: this.sanitizer.bypassSecurityTrustHtml(element.icon)
  }))
}
